<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信发送系统 - api.dailuanshej.cn</title>
    <meta name="description" content="轻量级短信发送系统，支持单发、批量发送">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>📱 短信发送系统</h1>
            <p class="subtitle">api.dailuanshej.cn - 轻量级短信发送平台</p>
        </header>

        <!-- 主功能区 -->
        <div class="main-section">
            <!-- 功能选项卡 -->
            <div class="function-tabs">
                <button class="tab-btn active" onclick="showSingleSMS()">单发短信</button>
                <button class="tab-btn" onclick="showBatchSMS()">批量发送</button>
            </div>

            <!-- 单发短信 -->
            <div id="singleSMSSection" class="function-section">
                <h3>📱 单发短信</h3>
                <form id="singleSMSForm">
                    <div class="form-group">
                        <label>接收号码</label>
                        <input type="tel" id="singleRecipient" placeholder="+8613800138000" required>
                    </div>
                    <div class="form-group">
                        <label>发送方ID</label>
                        <input type="text" id="singleSenderId" value="YourName" placeholder="发送方标识">
                    </div>
                    <div class="form-group">
                        <label>短信内容</label>
                        <textarea id="singleMessage" placeholder="请输入短信内容" rows="4" required></textarea>
                        <div class="char-count">
                            <span id="singleCharCount">0</span>/500
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">发送短信</button>
                </form>
            </div>

            <!-- 批量发送 -->
            <div id="batchSMSSection" class="function-section" style="display: none;">
                <h3>📢 批量发送</h3>
                <form id="batchSMSForm">
                    <div class="form-group">
                        <label>接收号码</label>
                        <textarea id="batchRecipients" placeholder="请输入多个手机号，每行一个或用逗号分隔&#10;+8613800138000&#10;+8613800138001" rows="5" required></textarea>
                        <div class="form-tip">支持每行一个号码或用逗号分隔，需要+86国家代码</div>
                    </div>
                    <div class="form-group">
                        <label>发送方ID</label>
                        <input type="text" id="batchSenderId" value="YourName" placeholder="发送方标识">
                    </div>
                    <div class="form-group">
                        <label>短信内容</label>
                        <textarea id="batchMessage" placeholder="请输入短信内容" rows="4" required></textarea>
                        <div class="char-count">
                            <span id="batchCharCount">0</span>/500
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">批量发送</button>
                </form>
            </div>
        </div>

        <!-- 结果显示 -->
        <div id="resultSection" class="result-section" style="display: none;">
            <h3>发送结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message" style="display: none;"></div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            setupCharCounters();
        });

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('singleSMSForm').addEventListener('submit', handleSingleSMS);
            document.getElementById('batchSMSForm').addEventListener('submit', handleBatchSMS);
        }

        // 设置字符计数器
        function setupCharCounters() {
            const textareas = [
                { textarea: 'singleMessage', counter: 'singleCharCount' },
                { textarea: 'batchMessage', counter: 'batchCharCount' }
            ];

            textareas.forEach(({ textarea, counter }) => {
                const element = document.getElementById(textarea);
                const counterElement = document.getElementById(counter);

                if (element && counterElement) {
                    element.addEventListener('input', function() {
                        counterElement.textContent = this.value.length;
                    });
                }
            });
        }

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const message = document.getElementById('message');
            message.querySelector('p').textContent = text;
            message.className = `message show ${type}`;
            setTimeout(() => { message.className = 'message'; }, 3000);
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'flex';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 显示单发短信
        function showSingleSMS() {
            document.getElementById('singleSMSSection').style.display = 'block';
            document.getElementById('batchSMSSection').style.display = 'none';
            
            // 更新选项卡状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 显示批量发送
        function showBatchSMS() {
            document.getElementById('singleSMSSection').style.display = 'none';
            document.getElementById('batchSMSSection').style.display = 'block';
            
            // 更新选项卡状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 处理单发短信
        async function handleSingleSMS(e) {
            e.preventDefault();

            const recipient = document.getElementById('singleRecipient').value;
            const senderId = document.getElementById('singleSenderId').value;
            const message = document.getElementById('singleMessage').value;

            if (!recipient || !message) {
                showMessage('请填写接收号码和短信内容', 'error');
                return;
            }

            await sendSMS([recipient], message, senderId);
        }

        // 处理批量发送
        async function handleBatchSMS(e) {
            e.preventDefault();

            const recipients = document.getElementById('batchRecipients').value;
            const senderId = document.getElementById('batchSenderId').value;
            const message = document.getElementById('batchMessage').value;

            if (!recipients || !message) {
                showMessage('请填写接收号码和短信内容', 'error');
                return;
            }

            // 解析号码
            const phoneList = recipients
                .split(/[\n\r,;，；]/)
                .map(phone => phone.trim())
                .filter(phone => phone.length > 0);

            if (phoneList.length === 0) {
                showMessage('请输入有效的手机号码', 'error');
                return;
            }

            await sendSMS(phoneList, message, senderId);
        }

        // 发送短信通用函数 - 直接调用GoTone API
        async function sendSMS(recipients, message, senderId = 'YourName') {
            console.log('=== 直接调用GoTone API发送短信 ===');
            console.log('接收号码:', recipients);
            console.log('短信内容:', message);
            console.log('发送方ID:', senderId);

            const GOTONE_API_URL = 'https://gotones.site/api/v3/sms/send';
            const GOTONE_API_TOKEN = '67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc';

            showLoading();

            try {
                // 处理接收号码格式 - GoTone需要逗号分隔的字符串
                const recipientList = Array.isArray(recipients)
                    ? recipients.join(',')
                    : recipients.toString();

                const requestData = {
                    recipient: recipientList,
                    sender_id: senderId,
                    type: 'plain',
                    message: message
                };

                console.log('GoTone API请求数据:', requestData);

                const response = await fetch(GOTONE_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${GOTONE_API_TOKEN}`
                    },
                    body: JSON.stringify(requestData)
                });

                const responseText = await response.text();
                console.log('GoTone API原始响应:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    showMessage('GoTone API响应格式错误: ' + responseText, 'error');
                    showResult({ error: '响应格式错误: ' + responseText }, false);
                    return;
                }

                console.log('GoTone API解析后的数据:', data);

                // 根据GoTone API文档，成功响应格式为 {"status": "success", "data": "..."}
                if (response.ok && data.status === 'success') {
                    const recipientCount = Array.isArray(recipients) ? recipients.length : 1;
                    showMessage(`✅ 短信发送成功！发送给 ${recipientCount} 个号码`, 'success');
                    showResult(data, true);
                } else {
                    // 失败响应格式为 {"status": "error", "message": "..."}
                    const errorMsg = data.message || data.error || `HTTP ${response.status}: ${response.statusText}`;
                    console.error('GoTone API发送失败:', errorMsg);
                    showMessage('❌ 发送失败: ' + errorMsg, 'error');
                    showResult(data, false);
                }
            } catch (error) {
                console.error('GoTone API网络错误:', error);
                showMessage('❌ 网络错误: ' + error.message, 'error');
                showResult({ error: error.message }, false);
            } finally {
                hideLoading();
            }
        }

        // 显示发送结果
        function showResult(data, success) {
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            resultContent.innerHTML = `
                <div class="${success ? 'result-success' : 'result-error'}">
                    <h4>${success ? '✅ 发送成功' : '❌ 发送失败'}</h4>
                    <p>${success ? data.message || '短信发送成功' : data.message || data.error}</p>
                    ${data.data ? `<pre>${JSON.stringify(data.data, null, 2)}</pre>` : ''}
                </div>
            `;

            resultSection.style.display = 'block';
        }
    </script>
</body>
</html>
