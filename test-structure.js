// 测试项目结构的脚本
const fs = require('fs');
const path = require('path');

console.log('🔍 检查项目结构...');

const requiredFiles = [
  'server.js',
  'package.json',
  'public/index.html',
  'public/css/style.css',
  'public/js/script.js',
  'public/assets/favicon.ico',
  'src/config/config.js',
  'src/routes/auth.js',
  'src/routes/sms.js',
  'src/middleware/auth.js',
  'src/utils/database.js',
  'src/utils/smsApi.js',
  'config/.env.example',
  'config/ecosystem.config.js',
  'config/nginx.conf',
  'scripts/deploy.sh',
  'data/users.json',
  'README.md'
];

const requiredDirs = [
  'public',
  'public/css',
  'public/js',
  'public/assets',
  'src',
  'src/config',
  'src/routes',
  'src/middleware',
  'src/utils',
  'config',
  'scripts',
  'data'
];

let allGood = true;

// 检查目录
console.log('\n📁 检查目录结构:');
requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}/`);
  } else {
    console.log(`❌ ${dir}/ - 缺失`);
    allGood = false;
  }
});

// 检查文件
console.log('\n📄 检查必需文件:');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 缺失`);
    allGood = false;
  }
});

// 检查package.json依赖
console.log('\n📦 检查package.json依赖:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    'express',
    'cors',
    'bcryptjs',
    'jsonwebtoken',
    'axios',
    'multer',
    'dotenv'
  ];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 缺失依赖`);
      allGood = false;
    }
  });
} catch (error) {
  console.log('❌ package.json 读取失败');
  allGood = false;
}

console.log('\n' + '='.repeat(50));
if (allGood) {
  console.log('🎉 项目结构检查通过！');
  console.log('📝 接下来可以运行:');
  console.log('   1. npm install');
  console.log('   2. 复制 config/.env.example 到 .env 并配置');
  console.log('   3. ./scripts/deploy.sh 或 npm start');
} else {
  console.log('⚠️  项目结构存在问题，请检查上述缺失的文件和目录');
}
console.log('='.repeat(50));
