// 应用配置文件
require('dotenv').config();

const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key-change-in-production',
    expiresIn: '24h'
  },

  // GoTone SMS API配置
  sms: {
    baseURL: 'https://gotones.site/api/v3',
    token: process.env.GOTONE_TOKEN || 'YOUR_GOTONE_TOKEN_HERE',
    timeout: 30000
  },

  // 文件上传配置
  upload: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['.txt', '.csv'],
    uploadDir: 'uploads'
  },

  // 数据库配置（JSON文件）
  database: {
    usersFile: './data/users.json'
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    logDir: './logs'
  }
};

module.exports = config;
