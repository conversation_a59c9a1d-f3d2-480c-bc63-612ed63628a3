<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>GoTone SMS 测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>GoTone SMS 直接测试</h1>
    <form id="testForm">
        <div class="form-group">
            <label>接收号码:</label>
            <input type="text" id="phone" placeholder="8613800138000" required>
        </div>
        <div class="form-group">
            <label>短信内容:</label>
            <textarea id="msg" placeholder="测试短信内容" required></textarea>
        </div>
        <button type="submit">直接发送到GoTone</button>
    </form>
    <div id="result"></div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('msg').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<p>正在发送...</p>';
            
            try {
                const response = await fetch('https://gotones.site/api/v3/sms/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': 'Bearer 67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc'
                    },
                    body: JSON.stringify({
                        recipient: phone,
                        sender_id: 'YourName',
                        type: 'plain',
                        message: message
                    })
                });
                
                const data = await response.json();
                console.log('GoTone响应:', data);
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = `<div class="result success">✅ 发送成功！<br>响应: ${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 发送失败: ${data.message}<br>完整响应: ${JSON.stringify(data, null, 2)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 网络错误: ${error.message}</div>`;
                console.error('错误:', error);
            }
        });
    </script>
</body>
</html>
