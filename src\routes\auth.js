// 认证路由
const express = require('express');
const bcrypt = require('bcryptjs');
const router = express.Router();

const database = require('../utils/database');
const { generateToken, authenticateToken, rateLimit } = require('../middleware/auth');

// 用户注册
router.post('/register', rateLimit(5, 15 * 60 * 1000), async (req, res) => {
  try {
    const { phone, password, username } = req.body;

    // 验证输入
    if (!phone || !password) {
      return res.status(400).json({ 
        success: false,
        error: '手机号和密码不能为空',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({ 
        success: false,
        error: '手机号格式不正确',
        code: 'INVALID_PHONE_FORMAT'
      });
    }

    // 验证密码强度
    if (password.length < 6) {
      return res.status(400).json({ 
        success: false,
        error: '密码长度不能少于6位',
        code: 'WEAK_PASSWORD'
      });
    }

    // 检查用户是否已存在
    const existingUser = database.findUserByPhone(phone);
    if (existingUser) {
      return res.status(400).json({ 
        success: false,
        error: '手机号已注册',
        code: 'PHONE_ALREADY_EXISTS'
      });
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const userData = {
      phone,
      username: username || phone,
      password: hashedPassword
    };

    const newUser = database.createUser(userData);

    // 返回成功响应（不包含密码）
    const { password: _, ...userResponse } = newUser;
    
    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: userResponse
      }
    });

  } catch (error) {
    console.error('用户注册失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '注册失败',
      code: 'REGISTRATION_FAILED'
    });
  }
});

// 用户登录
router.post('/login', rateLimit(10, 15 * 60 * 1000), async (req, res) => {
  try {
    const { phone, password } = req.body;

    // 验证输入
    if (!phone || !password) {
      return res.status(400).json({ 
        success: false,
        error: '手机号和密码不能为空',
        code: 'MISSING_CREDENTIALS'
      });
    }

    // 查找用户
    const user = database.findUserByPhone(phone);
    if (!user) {
      return res.status(400).json({ 
        success: false,
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(400).json({ 
        success: false,
        error: '密码错误',
        code: 'INVALID_PASSWORD'
      });
    }

    // 更新最后登录时间
    database.updateLastLogin(user.id);

    // 生成JWT token
    const userToken = generateToken(user);

    // 返回成功响应（不包含密码）
    const { password: _, ...userResponse } = user;

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token: userToken,
        user: userResponse
      }
    });

  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '登录失败',
      code: 'LOGIN_FAILED'
    });
  }
});

// 获取当前用户信息
router.get('/user', authenticateToken, (req, res) => {
  try {
    const { password: _, ...userResponse } = req.currentUser;
    
    res.json({
      success: true,
      message: '获取用户信息成功',
      data: {
        user: userResponse
      }
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({ 
      success: false,
      error: '获取用户信息失败',
      code: 'GET_USER_FAILED'
    });
  }
});

// 更新用户信息
router.put('/user', authenticateToken, async (req, res) => {
  try {
    const { username, oldPassword, newPassword } = req.body;
    const userId = req.user.userId;

    const updateData = {};

    // 更新用户名
    if (username && username !== req.currentUser.username) {
      updateData.username = username;
    }

    // 更新密码
    if (newPassword) {
      if (!oldPassword) {
        return res.status(400).json({ 
          success: false,
          error: '请提供当前密码',
          code: 'OLD_PASSWORD_REQUIRED'
        });
      }

      // 验证当前密码
      const isValidPassword = await bcrypt.compare(oldPassword, req.currentUser.password);
      if (!isValidPassword) {
        return res.status(400).json({ 
          success: false,
          error: '当前密码错误',
          code: 'INVALID_OLD_PASSWORD'
        });
      }

      // 验证新密码强度
      if (newPassword.length < 6) {
        return res.status(400).json({ 
          success: false,
          error: '新密码长度不能少于6位',
          code: 'WEAK_NEW_PASSWORD'
        });
      }

      // 加密新密码
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      updateData.password = hashedPassword;
    }

    // 如果没有要更新的数据
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({ 
        success: false,
        error: '没有要更新的数据',
        code: 'NO_UPDATE_DATA'
      });
    }

    // 更新用户信息
    const updatedUser = database.updateUser(userId, updateData);
    const { password: _, ...userResponse } = updatedUser;

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: {
        user: userResponse
      }
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '更新用户信息失败',
      code: 'UPDATE_USER_FAILED'
    });
  }
});

// 退出登录（客户端处理，服务端可以记录日志）
router.post('/logout', authenticateToken, (req, res) => {
  try {
    console.log(`用户 ${req.currentUser.phone} 退出登录`);
    
    res.json({
      success: true,
      message: '退出登录成功'
    });
  } catch (error) {
    console.error('退出登录失败:', error);
    res.status(500).json({ 
      success: false,
      error: '退出登录失败',
      code: 'LOGOUT_FAILED'
    });
  }
});

// 验证token接口（用于前端验证）
router.post('/verify', (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        error: 'Token不能为空',
        code: 'TOKEN_REQUIRED'
      });
    }

    const { verifyToken } = require('../middleware/auth');
    const result = verifyToken(token);

    if (result.success) {
      const { password: _, ...userResponse } = result.currentUser;
      res.json({
        success: true,
        message: 'Token验证成功',
        data: {
          user: userResponse
        }
      });
    } else {
      res.status(401).json({
        success: false,
        error: result.error,
        code: 'TOKEN_INVALID'
      });
    }
  } catch (error) {
    console.error('Token验证失败:', error);
    res.status(500).json({
      success: false,
      error: 'Token验证失败',
      code: 'TOKEN_VERIFY_FAILED'
    });
  }
});

module.exports = router;
