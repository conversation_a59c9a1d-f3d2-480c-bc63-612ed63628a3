#!/bin/bash

# 服务器环境检查脚本
echo "🔍 检查服务器环境..."

# 检查Node.js
echo ""
echo "📦 Node.js 环境:"
if command -v node &> /dev/null; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js 未安装"
    echo "   安装命令: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
    echo "            sudo apt-get install -y nodejs"
fi

# 检查npm
if command -v npm &> /dev/null; then
    echo "✅ npm: $(npm --version)"
else
    echo "❌ npm 未安装"
fi

# 检查PM2
echo ""
echo "🔧 进程管理:"
if command -v pm2 &> /dev/null; then
    echo "✅ PM2: $(pm2 --version)"
else
    echo "⚠️  PM2 未安装 (可选)"
    echo "   安装命令: npm install -g pm2"
fi

# 检查Nginx
echo ""
echo "🌐 Web服务器:"
if command -v nginx &> /dev/null; then
    echo "✅ Nginx: $(nginx -v 2>&1 | cut -d' ' -f3)"
else
    echo "⚠️  Nginx 未安装 (可选)"
    echo "   安装命令: sudo apt install nginx"
fi

# 检查端口
echo ""
echo "🔌 端口检查:"
if netstat -tlnp 2>/dev/null | grep :3000 &> /dev/null; then
    echo "⚠️  端口3000已被占用"
    echo "   占用进程: $(netstat -tlnp 2>/dev/null | grep :3000 | awk '{print $7}')"
else
    echo "✅ 端口3000可用"
fi

if netstat -tlnp 2>/dev/null | grep :80 &> /dev/null; then
    echo "ℹ️  端口80已被占用 (通常是Nginx)"
else
    echo "⚠️  端口80未被占用"
fi

# 检查防火墙
echo ""
echo "🔥 防火墙状态:"
if command -v ufw &> /dev/null; then
    ufw_status=$(sudo ufw status 2>/dev/null | head -1)
    echo "ℹ️  UFW: $ufw_status"
    if [[ $ufw_status == *"active"* ]]; then
        echo "   请确保端口3000和80已开放:"
        echo "   sudo ufw allow 3000"
        echo "   sudo ufw allow 80"
        echo "   sudo ufw allow 443"
    fi
elif command -v firewall-cmd &> /dev/null; then
    echo "ℹ️  FirewallD: $(sudo firewall-cmd --state 2>/dev/null || echo 'unknown')"
else
    echo "ℹ️  未检测到常见防火墙"
fi

# 检查域名解析
echo ""
echo "🌍 域名解析:"
if command -v nslookup &> /dev/null; then
    domain_ip=$(nslookup api.dailuanshej.cn 2>/dev/null | grep "Address:" | tail -1 | awk '{print $2}')
    if [ ! -z "$domain_ip" ]; then
        echo "✅ api.dailuanshej.cn 解析到: $domain_ip"
        
        # 检查是否解析到本机
        local_ip=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "unknown")
        if [ "$domain_ip" = "$local_ip" ]; then
            echo "✅ 域名解析到本机IP"
        else
            echo "⚠️  域名解析IP ($domain_ip) 与本机IP ($local_ip) 不匹配"
        fi
    else
        echo "❌ api.dailuanshej.cn 解析失败"
    fi
else
    echo "⚠️  nslookup 命令不可用"
fi

# 检查磁盘空间
echo ""
echo "💾 磁盘空间:"
df_output=$(df -h . 2>/dev/null | tail -1)
if [ ! -z "$df_output" ]; then
    available=$(echo $df_output | awk '{print $4}')
    used_percent=$(echo $df_output | awk '{print $5}')
    echo "ℹ️  可用空间: $available (已使用: $used_percent)"
else
    echo "⚠️  无法检查磁盘空间"
fi

# 检查内存
echo ""
echo "🧠 内存状态:"
if command -v free &> /dev/null; then
    memory_info=$(free -h | grep "Mem:")
    total_mem=$(echo $memory_info | awk '{print $2}')
    available_mem=$(echo $memory_info | awk '{print $7}')
    echo "ℹ️  总内存: $total_mem, 可用: $available_mem"
else
    echo "⚠️  无法检查内存状态"
fi

# 检查系统负载
echo ""
echo "⚡ 系统负载:"
if [ -f /proc/loadavg ]; then
    load_avg=$(cat /proc/loadavg | awk '{print $1, $2, $3}')
    echo "ℹ️  负载平均值: $load_avg"
else
    echo "⚠️  无法检查系统负载"
fi

# 总结
echo ""
echo "=" * 50
echo "📋 环境检查完成"
echo ""
echo "🚀 准备部署:"
echo "   1. 确保Node.js和npm已安装"
echo "   2. 运行: ./scripts/deploy.sh"
echo "   3. 配置: 编辑 .env 文件"
echo "   4. 测试: 访问 https://api.dailuanshej.cn"
echo ""
echo "💡 建议安装:"
echo "   - PM2: npm install -g pm2"
echo "   - Nginx: sudo apt install nginx"
echo ""
echo "🔧 如需帮助，请查看 DEPLOY.md 文件"
echo "=" * 50
