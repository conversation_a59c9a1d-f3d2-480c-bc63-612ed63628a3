// 认证中间件
const jwt = require('jsonwebtoken');
const config = require('../config/config');

// 服务器端session存储
const activeSessions = new Map();
const adminSessions = new Map();

// 清理过期session
setInterval(() => {
  const now = Date.now();
  // 清理用户session（24小时过期）
  for (const [sessionId, session] of activeSessions.entries()) {
    if (now - session.createdAt > 24 * 60 * 60 * 1000) {
      activeSessions.delete(sessionId);
    }
  }
  // 清理管理员session（24小时过期）
  for (const [sessionId, session] of adminSessions.entries()) {
    if (now - session.createdAt > 24 * 60 * 60 * 1000) {
      adminSessions.delete(sessionId);
    }
  }
}, 60 * 60 * 1000); // 每小时清理一次
const database = require('../utils/database');

// JWT验证中间件
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  console.log('认证请求:', req.url);
  console.log('User-Agent:', req.headers['user-agent']);
  console.log('Authorization头:', authHeader);
  console.log('提取的token:', token ? token.substring(0, 20) + '...' : 'null');

  if (!token) {
    console.log('缺少token');
    return res.status(401).json({
      success: false,
      error: '需要登录',
      code: 'UNAUTHORIZED'
    });
  }

  // 首先检查服务器端session
  if (global.userSessions && global.userSessions.has(token)) {
    const session = global.userSessions.get(token);
    console.log('使用服务器session:', session.user.phone);
    req.user = session.user;
    // 更新最后访问时间
    session.lastAccess = Date.now();
    return next();
  }

  jwt.verify(token, config.jwt.secret, (err, user) => {
    if (err) {
      console.error('Token验证失败:', err.message);
      return res.status(403).json({
        success: false,
        error: '无效的token',
        code: 'INVALID_TOKEN'
      });
    }

    // 验证用户是否仍然存在
    const currentUser = database.findUserById(user.userId);
    if (!currentUser) {
      return res.status(403).json({
        success: false,
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    req.user = user;
    req.currentUser = currentUser;
    next();
  });
}

// 可选的JWT验证中间件（不强制要求登录）
function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    req.user = null;
    req.currentUser = null;
    return next();
  }

  jwt.verify(token, config.jwt.secret, (err, user) => {
    if (err) {
      req.user = null;
      req.currentUser = null;
    } else {
      const currentUser = database.findUserById(user.userId);
      req.user = user;
      req.currentUser = currentUser;
    }
    next();
  });
}

// 生成JWT token
function generateToken(user) {
  const payload = {
    userId: user.id,
    phone: user.phone,
    username: user.username
  };

  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn
  });
}

// 验证token有效性（不通过中间件）
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, config.jwt.secret);
    const user = database.findUserById(decoded.userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }

    return {
      success: true,
      user: decoded,
      currentUser: user
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 刷新token
function refreshToken(oldToken) {
  try {
    const decoded = jwt.verify(oldToken, config.jwt.secret, { ignoreExpiration: true });
    const user = database.findUserById(decoded.userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }

    // 生成新token
    const newToken = generateToken(user);
    
    return {
      success: true,
      token: newToken,
      user: user
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 管理员权限验证中间件（如果需要）
function requireAdmin(req, res, next) {
  if (!req.currentUser || !req.currentUser.isAdmin) {
    return res.status(403).json({ 
      success: false,
      error: '需要管理员权限',
      code: 'ADMIN_REQUIRED'
    });
  }
  next();
}

// 速率限制中间件（简单实现）
const rateLimitStore = new Map();

function rateLimit(maxRequests = 100, windowMs = 15 * 60 * 1000) {
  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - windowMs;

    // 清理过期记录
    if (rateLimitStore.has(key)) {
      const requests = rateLimitStore.get(key).filter(time => time > windowStart);
      rateLimitStore.set(key, requests);
    } else {
      rateLimitStore.set(key, []);
    }

    const requests = rateLimitStore.get(key);
    
    if (requests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: '请求过于频繁，请稍后重试',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    requests.push(now);
    rateLimitStore.set(key, requests);
    next();
  };
}

// 请求日志中间件
function requestLogger(req, res, next) {
  const start = Date.now();
  const { method, url, ip } = req;
  const userAgent = req.get('User-Agent') || 'Unknown';

  console.log(`[${new Date().toISOString()}] ${method} ${url} - ${ip} - ${userAgent}`);

  // 记录响应时间
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${method} ${url} - ${res.statusCode} - ${duration}ms`);
  });

  next();
}

module.exports = {
  authenticateToken,
  optionalAuth,
  generateToken,
  verifyToken,
  refreshToken,
  requireAdmin,
  rateLimit,
  requestLogger,
  activeSessions,
  adminSessions
};
