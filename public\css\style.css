/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #667eea;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.subtitle {
    margin: 5px 0 0 0;
    color: #888;
    font-size: 14px;
    font-weight: 300;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 认证区域 */
.auth-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.auth-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    font-weight: 600;
}

.tab-btn:hover {
    color: #667eea;
}

/* 表单样式 */
.auth-form, .function-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.auth-form h2 {
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-tip {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

.char-count {
    text-align: right;
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
}

/* 主功能区 */
.main-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.function-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.function-tabs .tab-btn {
    flex: 1;
    padding: 15px;
    background: #f8f9fa;
    border: none;
    border-bottom: 3px solid transparent;
}

.function-tabs .tab-btn.active {
    background: white;
    border-bottom-color: #667eea;
}

.function-section {
    margin: 0;
    box-shadow: none;
}

.function-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 18px;
}

/* 导入区域 */
.import-area {
    border: 2px dashed #dee2e6;
    padding: 30px;
    text-align: center;
    border-radius: 8px;
    margin-bottom: 20px;
}

.imported-contacts {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
}

.contact-list {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.contact-item {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 4px;
    font-size: 12px;
}

/* 结果显示 */
.result-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.result-success {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.result-error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: white;
    margin-top: 15px;
    font-size: 16px;
}

/* 消息提示 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #28a745;
}

.message.error {
    background: #dc3545;
}

.message.info {
    background: #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 管理页面样式 */
.admin-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.admin-card h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.admin-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.admin-buttons .btn {
    flex: 1;
    min-width: 120px;
}

.status-info, .stats-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.status-info p, .stats-info p {
    margin: 5px 0;
}

.user-list {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    margin-bottom: 10px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.user-info {
    flex: 1;
}

.user-actions {
    display: flex;
    gap: 5px;
}

.user-actions .btn {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
    border: none;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-info {
    background: #17a2b8;
    color: white;
    border: none;
}

.btn-info:hover {
    background: #138496;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .function-tabs {
        flex-direction: column;
    }

    .function-tabs .tab-btn {
        border-bottom: 1px solid #dee2e6;
        border-right: none;
    }

    .function-tabs .tab-btn.active {
        border-bottom: 1px solid #dee2e6;
        border-left: 3px solid #667eea;
    }

    .admin-buttons {
        flex-direction: column;
    }

    .admin-buttons .btn {
        min-width: auto;
    }
}
