// 全局变量
let currentUser = null;
let authToken = null;
let importedPhones = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    setupCharCounters();
});

// 初始化应用
function initializeApp() {
    // 检查本地存储的token
    const token = localStorage.getItem('authToken');
    if (token) {
        authToken = token;
        verifyToken();
    }
}

// 验证token有效性
async function verifyToken() {
    if (!authToken) {
        console.log('没有token，显示登录页面');
        showAuthSection();
        return;
    }

    try {
        console.log('验证token:', authToken.substring(0, 20) + '...');

        const response = await fetch('/api/user', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        console.log('验证响应状态:', response.status);

        if (response.ok) {
            const result = await response.json();
            console.log('用户信息获取成功:', result);
            if (result.success && result.data && result.data.user) {
                currentUser = result.data.user;
                showMainSection();
            } else {
                throw new Error('用户数据格式错误');
            }
        } else {
            const errorData = await response.text();
            console.log('验证失败响应:', errorData);
            localStorage.removeItem('authToken');
            authToken = null;
            showAuthSection();
        }
    } catch (error) {
        console.error('Token验证失败:', error);
        localStorage.removeItem('authToken');
        authToken = null;
        showAuthSection();
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 登录表单
    document.getElementById('loginForm').addEventListener('submit', handleLogin);

    // 注册表单
    document.getElementById('registerForm').addEventListener('submit', handleRegister);

    // 单发短信表单
    document.getElementById('singleSMSForm').addEventListener('submit', handleSingleSMS);

    // 批量发送表单
    document.getElementById('batchSMSForm').addEventListener('submit', handleBatchSMS);
}

// 设置字符计数器
function setupCharCounters() {
    const textareas = [
        { textarea: 'singleMessage', counter: 'singleCharCount' },
        { textarea: 'batchMessage', counter: 'batchCharCount' },
        { textarea: 'importMessage', counter: 'importCharCount' }
    ];

    textareas.forEach(({ textarea, counter }) => {
        const element = document.getElementById(textarea);
        const counterElement = document.getElementById(counter);

        if (element && counterElement) {
            element.addEventListener('input', function() {
                counterElement.textContent = this.value.length;
                if (this.value.length > 500) {
                    counterElement.style.color = '#dc3545';
                } else {
                    counterElement.style.color = '#888';
                }
            });
        }
    });
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('message');
    messageEl.textContent = message;
    messageEl.className = `message ${type}`;
    messageEl.style.display = 'block';

    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}

// 显示加载状态
function showLoading() {
    document.getElementById('loading').style.display = 'flex';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// 显示认证区域
function showAuthSection() {
    document.getElementById('authSection').style.display = 'block';
    document.getElementById('mainSection').style.display = 'none';
    document.getElementById('userInfo').style.display = 'none';
}

// 显示主功能区
function showMainSection() {
    document.getElementById('authSection').style.display = 'none';
    document.getElementById('mainSection').style.display = 'block';
    document.getElementById('userInfo').style.display = 'flex';
    document.getElementById('username').textContent = currentUser.username || currentUser.phone;
}

// 切换到登录
function showLogin() {
    document.getElementById('loginForm').style.display = 'block';
    document.getElementById('registerForm').style.display = 'none';
    document.querySelectorAll('.auth-tabs .tab-btn')[0].classList.add('active');
    document.querySelectorAll('.auth-tabs .tab-btn')[1].classList.remove('active');
}

// 切换到注册
function showRegister() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'block';
    document.querySelectorAll('.auth-tabs .tab-btn')[0].classList.remove('active');
    document.querySelectorAll('.auth-tabs .tab-btn')[1].classList.add('active');
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const phone = document.getElementById('loginPhone').value;
    const password = document.getElementById('loginPassword').value;

    if (!phone || !password) {
        showMessage('请填写完整信息', 'error');
        return;
    }

    showLoading();

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ phone, password })
        });

        const data = await response.json();

        if (response.ok) {
            console.log('登录响应数据:', data);

            if (data.success && data.data && data.data.token) {
                authToken = data.data.token;
                currentUser = data.data.user;
                localStorage.setItem('authToken', authToken);

                console.log('Token已保存:', authToken.substring(0, 20) + '...');
                console.log('验证保存结果:', localStorage.getItem('authToken') ? '成功' : '失败');

                showMessage('登录成功', 'success');
                showMainSection();
            } else {
                console.error('登录响应格式错误:', data);
                showMessage('登录响应格式错误', 'error');
            }
        } else {
            console.error('登录失败:', data);
            showMessage(data.error || '登录失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    } finally {
        hideLoading();
    }
}

// 处理注册
async function handleRegister(e) {
    e.preventDefault();

    const phone = document.getElementById('registerPhone').value;
    const username = document.getElementById('registerUsername').value;
    const password = document.getElementById('registerPassword').value;

    if (!phone || !password) {
        showMessage('手机号和密码不能为空', 'error');
        return;
    }

    showLoading();

    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ phone, username, password })
        });

        const data = await response.json();

        if (response.ok) {
            showMessage('注册成功，请登录', 'success');
            showLogin();
            document.getElementById('loginPhone').value = phone;
        } else {
            showMessage(data.error || '注册失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    } finally {
        hideLoading();
    }
}

// 显示单发短信
function showSingleSMS() {
    const singleSection = document.getElementById('singleSMSSection');
    const batchSection = document.getElementById('batchSMSSection');
    const importSection = document.getElementById('importContactsSection');

    if (!singleSection || !batchSection || !importSection) {
        console.error('DOM元素未找到:', {
            singleSection: !!singleSection,
            batchSection: !!batchSection,
            importSection: !!importSection
        });
        return;
    }

    singleSection.style.display = 'block';
    batchSection.style.display = 'none';
    importSection.style.display = 'none';

    // 更新选项卡状态
    updateFunctionTabs(0);
}

// 显示批量发送
function showBatchSMS() {
    const singleSection = document.getElementById('singleSMSSection');
    const batchSection = document.getElementById('batchSMSSection');
    const importSection = document.getElementById('importContactsSection');

    if (!singleSection || !batchSection || !importSection) {
        console.error('DOM元素未找到:', {
            singleSection: !!singleSection,
            batchSection: !!batchSection,
            importSection: !!importSection
        });
        return;
    }

    singleSection.style.display = 'none';
    batchSection.style.display = 'block';
    importSection.style.display = 'none';

    // 更新选项卡状态
    updateFunctionTabs(1);
}

// 显示导入号码
function showImportContacts() {
    const singleSection = document.getElementById('singleSMSSection');
    const batchSection = document.getElementById('batchSMSSection');
    const importSection = document.getElementById('importContactsSection');

    if (!singleSection || !batchSection || !importSection) {
        console.error('DOM元素未找到:', {
            singleSection: !!singleSection,
            batchSection: !!batchSection,
            importSection: !!importSection
        });
        return;
    }

    singleSection.style.display = 'none';
    batchSection.style.display = 'none';
    importSection.style.display = 'block';

    // 更新选项卡状态
    updateFunctionTabs(2);
}



// 更新功能选项卡状态
function updateFunctionTabs(activeIndex) {
    const tabs = document.querySelectorAll('.function-tabs .tab-btn');
    tabs.forEach((tab, index) => {
        if (index === activeIndex) {
            tab.classList.add('active');
        } else {
            tab.classList.remove('active');
        }
    });
}

// 处理单发短信
async function handleSingleSMS(e) {
    e.preventDefault();

    const recipient = document.getElementById('singleRecipient').value;
    const senderId = document.getElementById('singleSenderId').value;
    const message = document.getElementById('singleMessage').value;

    if (!recipient || !message) {
        showMessage('请填写接收号码和短信内容', 'error');
        return;
    }

    await sendSMS([recipient], message, senderId);
}

// 处理批量发送
async function handleBatchSMS(e) {
    e.preventDefault();

    const recipients = document.getElementById('batchRecipients').value;
    const senderId = document.getElementById('batchSenderId').value;
    const message = document.getElementById('batchMessage').value;

    if (!recipients || !message) {
        showMessage('请填写接收号码和短信内容', 'error');
        return;
    }

    // 解析号码
    const phoneList = recipients
        .split(/[\n\r,;]/)
        .map(phone => phone.trim())
        .filter(phone => phone.length > 0);

    if (phoneList.length === 0) {
        showMessage('请输入有效的手机号码', 'error');
        return;
    }

    await sendSMS(phoneList, message, senderId);
}

// 发送短信通用函数 - 直接调用GoTone API
async function sendSMS(recipients, message, senderId = 'YourName') {
    console.log('=== 直接调用GoTone API发送短信 ===');
    console.log('接收号码:', recipients);
    console.log('短信内容:', message);
    console.log('发送方ID:', senderId);

    // GoTone API配置 - 官方真实配置
    const GOTONE_API_URL = 'https://gotones.site/api/v3/sms/send';
    const GOTONE_API_TOKEN = '67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc';

    showLoading();

    try {
        // 处理接收号码格式 - GoTone需要逗号分隔的字符串
        const recipientList = Array.isArray(recipients)
            ? recipients.join(',')
            : recipients.toString();

        const requestData = {
            recipient: recipientList,  // 注意：GoTone使用 recipient 而不是 recipients
            sender_id: 'SMS',         // 使用简单的发送方ID，避免权限问题
            type: 'plain',            // GoTone要求的消息类型
            message: message
        };

        console.log('GoTone API请求数据:', requestData);
        console.log('API端点:', GOTONE_API_URL);

        const response = await fetch(GOTONE_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${GOTONE_API_TOKEN}`
            },
            body: JSON.stringify(requestData)
        });

        console.log('GoTone API响应状态:', response.status);
        console.log('响应状态文本:', response.statusText);

        const responseText = await response.text();
        console.log('GoTone API原始响应:', responseText);

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON解析失败:', parseError);
            showMessage('GoTone API响应格式错误: ' + responseText, 'error');
            showResult({ error: '响应格式错误: ' + responseText }, false);
            return;
        }

        console.log('GoTone API解析后的数据:', data);

        // 根据GoTone API文档，成功响应格式为 {"status": "success", "data": "..."}
        if (response.ok && data.status === 'success') {
            const recipientCount = Array.isArray(recipients) ? recipients.length : 1;
            showMessage(`✅ 短信发送成功！发送给 ${recipientCount} 个号码`, 'success');
            showResult(data, true);
        } else {
            // 失败响应格式为 {"status": "error", "message": "..."}
            const errorMsg = data.message || data.error || `HTTP ${response.status}: ${response.statusText}`;
            console.error('GoTone API发送失败:', errorMsg);
            showMessage('❌ 发送失败: ' + errorMsg, 'error');
            showResult(data, false);
        }
    } catch (error) {
        console.error('GoTone API网络错误:', error);
        showMessage('❌ 网络错误: ' + error.message, 'error');
        showResult({ error: error.message }, false);
    } finally {
        hideLoading();
    }
}

// 显示发送结果
function showResult(data, success) {
    const resultSection = document.getElementById('resultSection');
    const resultContent = document.getElementById('resultContent');

    resultContent.innerHTML = `
        <div class="${success ? 'result-success' : 'result-error'}">
            <h4>${success ? '✅ 发送成功' : '❌ 发送失败'}</h4>
            <p>${success ? data.message : data.error}</p>
            ${data.data ? `<pre>${JSON.stringify(data.data, null, 2)}</pre>` : ''}
        </div>
    `;

    resultSection.style.display = 'block';
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

// 导入联系人
async function importContacts() {
    const fileInput = document.getElementById('contactFile');
    const file = fileInput.files[0];

    if (!file) {
        showMessage('请选择文件', 'error');
        return;
    }

    showLoading();

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/import-contacts', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        const data = await response.json();

        if (response.ok) {
            importedPhones = data.phones;
            showMessage(data.message, 'success');
            displayImportedContacts();
        } else {
            showMessage(data.error || '导入失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    } finally {
        hideLoading();
    }
}

// 显示导入的联系人
function displayImportedContacts() {
    const contactList = document.getElementById('contactList');
    const importedSection = document.getElementById('importedContacts');

    contactList.innerHTML = importedPhones
        .map(phone => `<span class="contact-item">${phone}</span>`)
        .join('');

    importedSection.style.display = 'block';
}

// 发送给导入的联系人
async function sendToImportedContacts() {
    const senderId = document.getElementById('importSenderId').value;
    const message = document.getElementById('importMessage').value;

    if (!message) {
        showMessage('请输入短信内容', 'error');
        return;
    }

    if (importedPhones.length === 0) {
        showMessage('没有导入的联系人', 'error');
        return;
    }

    await sendSMS(importedPhones, message, senderId);
}

// 退出登录
function logout() {
    localStorage.removeItem('authToken');
    authToken = null;
    currentUser = null;
    importedPhones = [];
    showMessage('已退出登录', 'info');
    showAuthSection();

    // 清空表单
    document.querySelectorAll('form').forEach(form => form.reset());
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('importedContacts').style.display = 'none';
}



