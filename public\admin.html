<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS系统管理后台 - api.dailuanshej.cn</title>
    <meta name="description" content="SMS系统管理后台，系统监控、用户管理、令牌配置">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="stylesheet" href="/css/admin.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="logo">
                <h1>🎛️ SMS系统管理后台</h1>
                <p class="subtitle">api.dailuanshej.cn - 系统管理控制台</p>
            </div>
            <div id="adminInfo" class="admin-info" style="display: none;">
                <span>管理员：<span id="adminName"></span></span>
                <button onclick="logout()" class="btn btn-secondary">退出</button>
            </div>
        </header>

        <!-- 管理员登录表单 -->
        <div id="adminAuthSection" class="auth-section">
            <div class="auth-card">
                <h2>🔐 管理员登录</h2>
                <form id="adminLoginForm" class="auth-form">
                    <div class="form-group">
                        <label>管理员账号</label>
                        <input type="text" id="adminUsername" placeholder="请输入管理员账号" required>
                    </div>
                    <div class="form-group">
                        <label>管理密码</label>
                        <input type="password" id="adminPassword" placeholder="请输入管理密码" required>
                    </div>
                    <button type="submit" class="btn btn-primary">登录管理后台</button>
                </form>
                <div class="auth-footer">
                    <a href="/" class="link">← 返回客户页面</a>
                </div>
            </div>
        </div>

        <!-- 管理主界面 -->
        <div id="adminMainSection" class="admin-main" style="display: none;">
            <!-- 导航菜单 -->
            <nav class="admin-nav">
                <button class="nav-btn active" onclick="showDashboard()">📊 仪表盘</button>
                <button class="nav-btn" onclick="showSystemControl()">⚙️ 系统控制</button>
                <button class="nav-btn" onclick="showTokenManage()">🔑 令牌管理</button>
                <button class="nav-btn" onclick="showUserManage()">👥 用户管理</button>
                <button class="nav-btn" onclick="showLogs()">📋 日志管理</button>
                <button class="nav-btn" onclick="showStats()">📈 统计分析</button>
            </nav>

            <!-- 仪表盘 -->
            <div id="dashboardSection" class="admin-section">
                <h3>📊 系统仪表盘</h3>
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h4>🟢 系统状态</h4>
                        <div id="systemStatus" class="status-display">
                            <p>正在加载...</p>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <h4>📱 今日发送</h4>
                        <div id="todayStats" class="stats-display">
                            <span class="big-number">0</span>
                            <span class="unit">条短信</span>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <h4>👥 用户总数</h4>
                        <div id="userCount" class="stats-display">
                            <span class="big-number">0</span>
                            <span class="unit">个用户</span>
                        </div>
                    </div>
                    <div class="dashboard-card">
                        <h4>✅ 成功率</h4>
                        <div id="successRate" class="stats-display">
                            <span class="big-number">100</span>
                            <span class="unit">%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统控制 -->
            <div id="systemControlSection" class="admin-section" style="display: none;">
                <h3>⚙️ 系统控制</h3>
                <div class="control-grid">
                    <div class="control-card">
                        <h4>🔄 系统重启</h4>
                        <p>重启Node.js应用，加载新配置</p>
                        <button onclick="restartSystem()" class="btn btn-warning">重启系统</button>
                    </div>
                    <div class="control-card">
                        <h4>🗑️ 清空日志</h4>
                        <p>清空所有系统日志文件</p>
                        <button onclick="clearLogs()" class="btn btn-secondary">清空日志</button>
                    </div>
                    <div class="control-card">
                        <h4>📥 导出日志</h4>
                        <p>下载系统日志文件</p>
                        <button onclick="exportLogs()" class="btn btn-info">导出日志</button>
                    </div>
                    <div class="control-card">
                        <h4>🔄 刷新数据</h4>
                        <p>刷新所有管理数据</p>
                        <button onclick="refreshAllData()" class="btn btn-primary">刷新数据</button>
                    </div>
                </div>
            </div>

            <!-- 令牌管理 -->
            <div id="tokenManageSection" class="admin-section" style="display: none;">
                <h3>🔑 API令牌管理</h3>
                <div class="token-card">
                    <h4>GoTone API配置</h4>
                    <div class="form-group">
                        <label>当前API令牌</label>
                        <input type="text" id="currentToken" placeholder="当前令牌" readonly>
                    </div>
                    <div class="form-group">
                        <label>新的API令牌</label>
                        <input type="text" id="newToken" placeholder="输入新的GoTone API令牌">
                        <small>更新令牌后系统将自动重启</small>
                    </div>
                    <div class="form-group">
                        <label>API端点</label>
                        <input type="text" value="https://gotones.site/api/v3" readonly>
                    </div>
                    <button onclick="updateApiToken()" class="btn btn-primary">更新令牌</button>
                </div>
            </div>

            <!-- 用户管理 -->
            <div id="userManageSection" class="admin-section" style="display: none;">
                <h3>👥 用户管理</h3>
                <div class="user-management">
                    <div class="user-controls">
                        <button onclick="refreshUsers()" class="btn btn-secondary">刷新用户</button>
                        <button onclick="exportUsers()" class="btn btn-info">导出用户</button>
                    </div>
                    <div id="userList" class="user-list">
                        <p>正在加载用户列表...</p>
                    </div>
                </div>
            </div>

            <!-- 日志管理 -->
            <div id="logsSection" class="admin-section" style="display: none;">
                <h3>📋 日志管理</h3>
                <div class="logs-container">
                    <div class="logs-controls">
                        <button onclick="refreshLogs()" class="btn btn-secondary">刷新日志</button>
                        <button onclick="clearLogs()" class="btn btn-warning">清空日志</button>
                        <button onclick="exportLogs()" class="btn btn-info">导出日志</button>
                    </div>
                    <div id="logsContent" class="logs-content">
                        <p>正在加载日志...</p>
                    </div>
                </div>
            </div>

            <!-- 统计分析 -->
            <div id="statsSection" class="admin-section" style="display: none;">
                <h3>📈 统计分析</h3>
                <div id="detailedStats" class="stats-container">
                    <p>正在加载统计数据...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message" style="display: none;"></div>

    <script src="/js/admin.js"></script>
</body>
</html>
