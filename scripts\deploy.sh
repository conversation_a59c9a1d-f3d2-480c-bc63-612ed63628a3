#!/bin/bash

# 轻量级短信系统部署脚本
# 使用方法: ./scripts/deploy.sh

set -e

echo "🚀 开始部署轻量级短信系统..."
echo "📁 当前路径: $(pwd)"
echo "🌐 检测到1Panel + OpenResty环境"
echo "=================================================="

# 获取脚本所在目录的父目录（项目根目录）
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "📁 项目目录: $PROJECT_ROOT"

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js"
    echo "请先安装Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js环境检查通过 ($(node --version))"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm"
    exit 1
fi

echo "✅ npm环境检查通过 ($(npm --version))"

# 安装依赖
echo "📦 安装项目依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p data
mkdir -p uploads
mkdir -p logs
mkdir -p public/assets

# 设置权限
chmod 755 data
chmod 755 uploads
chmod 755 logs
chmod 755 public/assets

# 复制环境配置文件
if [ ! -f ".env" ]; then
    echo "📋 创建环境配置文件..."
    cp config/.env.example .env
    echo "⚠️  请编辑 .env 文件配置您的GoTone API Token"
fi

# 创建favicon（如果不存在）
if [ ! -f "public/assets/favicon.ico" ]; then
    echo "🎨 创建默认favicon..."
    # 这里可以放置一个默认的favicon文件
    touch public/assets/favicon.ico
fi

# 删除旧的文件（如果存在）
echo "🧹 清理旧文件..."
rm -f public/style.css 2>/dev/null || true
rm -f public/script.js 2>/dev/null || true

# 检查PM2
if command -v pm2 &> /dev/null; then
    echo "🔄 使用PM2启动应用..."
    
    # 停止旧的进程（如果存在）
    pm2 delete sms-system 2>/dev/null || true
    
    # 启动新进程
    pm2 start config/ecosystem.config.js --env production
    pm2 save
    
    echo "✅ 应用已通过PM2启动"
    echo "📊 PM2状态:"
    pm2 list
else
    echo "💡 建议安装PM2进行进程管理:"
    echo "   npm install -g pm2"
    echo "   然后运行: pm2 start config/ecosystem.config.js --env production"
    echo ""
    echo "🔄 使用Node.js直接启动..."
    
    # 停止可能运行的进程
    pkill -f "node server.js" 2>/dev/null || true
    
    # 启动应用
    nohup node server.js > logs/app.log 2>&1 &
    echo "✅ 应用已启动（后台运行）"
fi

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 3

# 测试应用是否正常运行
if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 应用启动成功！"
else
    echo "⚠️  应用可能未正常启动，请检查日志"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📝 接下来的步骤:"
echo "   1. 编辑 .env 文件，配置您的GoTone API Token"
echo "   2. 配置Nginx反向代理（可选）:"
echo "      sudo cp config/nginx.conf /etc/nginx/sites-available/sms-system"
echo "      sudo ln -s /etc/nginx/sites-available/sms-system /etc/nginx/sites-enabled/"
echo "      sudo nginx -t && sudo systemctl reload nginx"
echo "   3. 设置域名解析到服务器IP"
echo "   4. 访问 https://api.dailuanshej.cn 测试（域名已解析）"
echo ""
echo "🔧 常用命令:"
echo "   查看日志: tail -f logs/app.log"
echo "   重启应用: pm2 restart sms-system"
echo "   停止应用: pm2 stop sms-system"
echo "   查看状态: pm2 status"
echo ""
echo "🌐 访问地址:"
echo "   本地: http://localhost:3000"
echo "   生产: https://api.dailuanshej.cn (域名已解析)"
echo ""
echo "📂 项目结构:"
echo "   前端文件: public/"
echo "   源代码: src/"
echo "   配置文件: config/"
echo "   数据文件: data/"
echo "   日志文件: logs/"
