// 短信路由
const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const router = express.Router();

const smsApi = require('../utils/smsApi');
const { authenticateToken, rateLimit } = require('../middleware/auth');
const config = require('../config/config');

// 文件上传配置
const upload = multer({ 
  dest: config.upload.uploadDir,
  limits: {
    fileSize: config.upload.maxFileSize
  },
  fileFilter: (req, file, cb) => {
    const ext = path.extname(file.originalname).toLowerCase();
    if (config.upload.allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型，请上传 .txt 或 .csv 文件'));
    }
  }
});

// 发送短信
router.post('/send', authenticateToken, rateLimit(20, 15 * 60 * 1000), async (req, res) => {
  try {
    const { recipients, message, sender_id = 'YourName', type = 'plain' } = req.body;

    // 验证输入
    if (!recipients || !message) {
      return res.status(400).json({ 
        success: false,
        error: '接收号码和短信内容不能为空',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 验证短信内容长度
    if (message.length > 500) {
      return res.status(400).json({ 
        success: false,
        error: '短信内容不能超过500字符',
        code: 'MESSAGE_TOO_LONG'
      });
    }

    // 处理接收者
    let recipientList = [];
    if (Array.isArray(recipients)) {
      recipientList = recipients;
    } else if (typeof recipients === 'string') {
      recipientList = recipients.split(/[,;，；]/).map(phone => phone.trim());
    } else {
      return res.status(400).json({ 
        success: false,
        error: '接收号码格式错误',
        code: 'INVALID_RECIPIENTS_FORMAT'
      });
    }

    // 验证号码格式
    const validation = smsApi.validatePhoneNumbers(recipientList);
    if (validation.invalidCount > 0) {
      return res.status(400).json({ 
        success: false,
        error: `发现 ${validation.invalidCount} 个无效号码`,
        code: 'INVALID_PHONE_NUMBERS',
        data: {
          invalidPhones: validation.invalid
        }
      });
    }

    // 限制单次发送数量
    if (validation.validCount > 1000) {
      return res.status(400).json({ 
        success: false,
        error: '单次发送不能超过1000个号码',
        code: 'TOO_MANY_RECIPIENTS'
      });
    }

    // 调用SMS API发送短信
    const result = await smsApi.sendSMS(validation.valid, message, sender_id, type);

    // 记录发送日志
    console.log(`用户 ${req.currentUser.phone} 发送短信给 ${validation.validCount} 个号码`);

    res.json({
      success: true,
      message: `短信发送成功！发送给 ${validation.validCount} 个号码`,
      data: {
        recipientCount: validation.validCount,
        apiResponse: result.data
      }
    });

  } catch (error) {
    console.error('短信发送失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '短信发送失败',
      code: 'SMS_SEND_FAILED'
    });
  }
});

// 批量发送短信
router.post('/batch', authenticateToken, rateLimit(5, 15 * 60 * 1000), async (req, res) => {
  try {
    const { recipients, message, sender_id = 'YourName' } = req.body;

    // 验证输入
    if (!recipients || !message) {
      return res.status(400).json({ 
        success: false,
        error: '接收号码和短信内容不能为空',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 处理批量号码
    let phoneList = [];
    if (typeof recipients === 'string') {
      phoneList = recipients
        .split(/[\n\r,;，；]/)
        .map(phone => phone.trim())
        .filter(phone => phone.length > 0);
    } else if (Array.isArray(recipients)) {
      phoneList = recipients;
    } else {
      return res.status(400).json({ 
        success: false,
        error: '号码格式错误',
        code: 'INVALID_RECIPIENTS_FORMAT'
      });
    }

    if (phoneList.length === 0) {
      return res.status(400).json({ 
        success: false,
        error: '请输入有效的手机号码',
        code: 'NO_VALID_RECIPIENTS'
      });
    }

    // 使用批量发送API
    const result = await smsApi.sendBatchSMS(phoneList, message, sender_id);

    // 记录发送日志
    console.log(`用户 ${req.currentUser.phone} 批量发送短信给 ${phoneList.length} 个号码`);

    res.json({
      success: true,
      message: `批量短信发送成功！发送给 ${phoneList.length} 个号码`,
      data: {
        recipientCount: phoneList.length,
        apiResponse: result.data
      }
    });

  } catch (error) {
    console.error('批量短信发送失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '批量短信发送失败',
      code: 'BATCH_SMS_SEND_FAILED'
    });
  }
});

// 导入联系人
router.post('/import-contacts', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        success: false,
        error: '请选择文件',
        code: 'FILE_REQUIRED'
      });
    }

    // 读取文件内容
    const fileContent = fs.readFileSync(req.file.path, 'utf8');
    
    // 解析号码
    const parseResult = smsApi.parsePhoneNumbers(fileContent);
    
    // 删除临时文件
    fs.unlinkSync(req.file.path);

    if (parseResult.count === 0) {
      return res.status(400).json({ 
        success: false,
        error: '文件中没有找到有效的手机号码',
        code: 'NO_VALID_PHONES_IN_FILE'
      });
    }

    // 验证号码格式
    const validation = smsApi.validatePhoneNumbers(parseResult.phones);

    // 记录导入日志
    console.log(`用户 ${req.currentUser.phone} 导入了 ${validation.validCount} 个号码`);

    res.json({
      success: true,
      message: `成功导入 ${validation.validCount} 个有效号码`,
      data: {
        phones: validation.valid,
        totalCount: parseResult.count,
        validCount: validation.validCount,
        invalidCount: validation.invalidCount,
        invalidPhones: validation.invalid
      }
    });

  } catch (error) {
    // 清理临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    console.error('联系人导入失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '联系人导入失败',
      code: 'IMPORT_CONTACTS_FAILED'
    });
  }
});

// 获取短信发送记录
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, status, date_from, date_to } = req.query;

    const params = {
      page: parseInt(page),
      limit: parseInt(limit)
    };

    if (status) params.status = status;
    if (date_from) params.date_from = date_from;
    if (date_to) params.date_to = date_to;

    const result = await smsApi.getSMSHistory(params);

    res.json({
      success: true,
      message: '获取短信记录成功',
      data: result.data
    });

  } catch (error) {
    console.error('获取短信记录失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '获取短信记录失败',
      code: 'GET_SMS_HISTORY_FAILED'
    });
  }
});

// 获取账户余额
router.get('/balance', authenticateToken, async (req, res) => {
  try {
    const result = await smsApi.getBalance();

    res.json({
      success: true,
      message: '获取账户余额成功',
      data: result.data
    });

  } catch (error) {
    console.error('获取账户余额失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || '获取账户余额失败',
      code: 'GET_BALANCE_FAILED'
    });
  }
});

// 测试API连接
router.get('/test-connection', authenticateToken, async (req, res) => {
  try {
    const result = await smsApi.testConnection();

    res.json({
      success: true,
      message: 'API连接测试成功',
      data: result.data
    });

  } catch (error) {
    console.error('API连接测试失败:', error);
    res.status(500).json({ 
      success: false,
      error: error.message || 'API连接测试失败',
      code: 'API_CONNECTION_TEST_FAILED'
    });
  }
});

module.exports = router;
