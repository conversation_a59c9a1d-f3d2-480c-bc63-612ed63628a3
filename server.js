const express = require('express');
const cors = require('cors');
const path = require('path');

// 导入配置和工具
const config = require('./src/config/config');
const { requestLogger } = require('./src/middleware/auth');

// 导入路由
const authRoutes = require('./src/routes/auth');
const smsRoutes = require('./src/routes/sms');
const adminRoutes = require('./src/routes/admin');

const app = express();
const PORT = config.server.port;

// 中间件 - 支持指纹浏览器
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'User-Agent'],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(requestLogger);

// 静态文件服务
app.use(express.static('public'));

// 管理后台路由
app.get('/admin.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});
app.use('/uploads', express.static('uploads'));

// 确保必要目录存在
const requiredDirs = ['data', 'uploads', 'logs', 'public/assets'];
requiredDirs.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!require('fs').existsSync(dirPath)) {
    require('fs').mkdirSync(dirPath, { recursive: true });
  }
});

// API路由
console.log('正在挂载API路由...');
app.use('/api', authRoutes);           // 支持 /api/login, /api/register
app.use('/api/auth', authRoutes);      // 同时支持 /api/auth/login, /api/auth/register
app.use('/api/sms', smsRoutes);
app.use('/api/admin', adminRoutes);
console.log('API路由挂载完成');

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({ message: 'API路由工作正常', timestamp: new Date().toISOString() });
});

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 健康检查
app.get('/health', (req, res) => {
  try {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message
    });
  }
});

// 简单的API健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: '页面不存在',
    code: 'NOT_FOUND'
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('全局错误:', error);

  res.status(500).json({
    success: false,
    error: config.server.env === 'production' ? '服务器内部错误' : error.message,
    code: 'INTERNAL_SERVER_ERROR'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 轻量级短信发送系统启动成功！');
  console.log(`📱 本地访问: http://localhost:${PORT}`);
  console.log(`🌐 生产环境: https://api.dailuanshej.cn (域名已解析)`);
  console.log(`⚙️  环境: ${config.server.env}`);
  console.log(`📁 项目结构: 有组织的文件夹`);
  console.log('');
  console.log('📂 文件夹结构:');
  console.log('   ├── public/          # 前端文件');
  console.log('   │   ├── css/         # 样式文件');
  console.log('   │   ├── js/          # JavaScript文件');
  console.log('   │   └── assets/      # 静态资源');
  console.log('   ├── src/             # 源代码');
  console.log('   │   ├── routes/      # 路由文件');
  console.log('   │   ├── middleware/  # 中间件');
  console.log('   │   ├── utils/       # 工具函数');
  console.log('   │   └── config/      # 配置文件');
  console.log('   ├── config/          # 部署配置');
  console.log('   ├── scripts/         # 脚本文件');
  console.log('   ├── data/            # 数据存储');
  console.log('   └── logs/            # 日志文件');
  console.log('');
  console.log('⚠️  请确保已配置 GOTONE_TOKEN 环境变量');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});

module.exports = app;
