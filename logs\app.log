nohup: ignoring input
[dotenv@17.2.1] injecting env (4) from .env -- tip: ⚙️  enable debug logging with { debug: true }
正在挂载API路由...
API路由挂载完成
🚀 轻量级短信发送系统启动成功！
📱 本地访问: http://localhost:3000
🌐 生产环境: https://api.dailuanshej.cn (域名已解析)
⚙️  环境: production
📁 项目结构: 有组织的文件夹

📂 文件夹结构:
   ├── public/          # 前端文件
   │   ├── css/         # 样式文件
   │   ├── js/          # JavaScript文件
   │   └── assets/      # 静态资源
   ├── src/             # 源代码
   │   ├── routes/      # 路由文件
   │   ├── middleware/  # 中间件
   │   ├── utils/       # 工具函数
   │   └── config/      # 配置文件
   ├── config/          # 部署配置
   ├── scripts/         # 脚本文件
   ├── data/            # 数据存储
   └── logs/            # 日志文件

⚠️  请确保已配置 GOTONE_TOKEN 环境变量
[2025-08-04T05:36:20.857Z] POST /api/login - ::ffff:127.0.0.1 - curl/7.81.0
[2025-08-04T05:36:20.976Z] POST /api/login - 200 - 119ms
[2025-08-04T05:37:24.732Z] POST /api/admin/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
管理员登录请求: { username: 'admin', password: 'admin123' }
验证账号: "admin", 期望: "admin"
验证密码: "admin123", 期望: "admin123"
账号匹配: true
密码匹配: true
创建管理员session: admin_session_1754285844736_c4ncti8ff
[2025-08-04T05:37:24.737Z] POST /api/admin/login - 200 - 5ms
[2025-08-04T05:37:25.440Z] GET /api/admin/token - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
管理员认证请求: /token
管理员token: admin_session_175428...
使用管理员session: admin
[2025-08-04T05:37:25.444Z] GET /api/admin/token - 304 - 4ms
[2025-08-04T05:37:25.680Z] GET /api/admin/status - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
管理员认证请求: /status
管理员token: admin_session_175428...
使用管理员session: admin
[2025-08-04T05:37:25.681Z] GET /api/admin/status - 200 - 2ms
[2025-08-04T05:37:26.974Z] GET /api/admin/stats - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
管理员认证请求: /stats
管理员token: admin_session_175428...
使用管理员session: admin
[2025-08-04T05:37:26.976Z] GET /api/admin/stats - 304 - 2ms
[2025-08-04T05:37:27.533Z] GET /api/admin/users - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
管理员认证请求: /users
管理员token: admin_session_175428...
使用管理员session: admin
[2025-08-04T05:37:27.534Z] GET /api/admin/users - 304 - 1ms
[2025-08-04T05:37:37.960Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T05:37:38.057Z] POST /api/login - 200 - 97ms
[2025-08-04T05:37:48.225Z] GET /api/admin/token - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
管理员认证请求: /token
管理员token: admin_session_175428...
使用管理员session: admin
[2025-08-04T05:37:48.226Z] GET /api/admin/token - 304 - 1ms
[2025-08-04T05:38:48.486Z] GET /api/user - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
认证请求: /user
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Authorization头: Bearer undefined
提取的token: undefined...
Token验证失败: jwt malformed
[2025-08-04T05:38:48.489Z] GET /api/user - 403 - 3ms
[2025-08-04T05:39:05.068Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T05:39:05.168Z] POST /api/login - 200 - 100ms
[2025-08-04T05:39:10.043Z] GET /api/user - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
认证请求: /user
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Authorization头: Bearer undefined
提取的token: undefined...
Token验证失败: jwt malformed
[2025-08-04T05:39:10.044Z] GET /api/user - 403 - 1ms
[2025-08-04T05:46:07.494Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T05:46:07.611Z] POST /api/login - 200 - 117ms
[2025-08-04T05:46:47.938Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T05:46:48.033Z] POST /api/login - 200 - 95ms
[2025-08-04T05:47:15.438Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T05:47:15.545Z] POST /api/login - 200 - 107ms
[2025-08-04T05:47:36.933Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T05:47:37.029Z] POST /api/login - 200 - 96ms
[2025-08-04T05:47:43.753Z] GET /api/user - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
认证请求: /user
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Authorization头: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIxNzU0MjM2NzEyMjczIiwicGhvbmUiOiIxMzcwMDEzNzAwMiIsInVzZXJuYW1lIjoiMTM3MDAxMzcwMDIiLCJpYXQiOjE3NTQyODY0NTcsImV4cCI6MTc1NDM3Mjg1N30.-nKte4K1YS49QZpM0rwvtm_RFwNj8i6eLwpaQwt6tVM
提取的token: eyJhbGciOiJIUzI1NiIs...
[2025-08-04T05:47:43.756Z] GET /api/user - 200 - 3ms
[2025-08-04T05:47:48.569Z] GET /api/user - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
认证请求: /user
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Authorization头: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIxNzU0MjM2NzEyMjczIiwicGhvbmUiOiIxMzcwMDEzNzAwMiIsInVzZXJuYW1lIjoiMTM3MDAxMzcwMDIiLCJpYXQiOjE3NTQyODY0NTcsImV4cCI6MTc1NDM3Mjg1N30.-nKte4K1YS49QZpM0rwvtm_RFwNj8i6eLwpaQwt6tVM
提取的token: eyJhbGciOiJIUzI1NiIs...
[2025-08-04T05:47:48.571Z] GET /api/user - 304 - 2ms
[2025-08-04T07:29:48.522Z] GET / - ::ffff:************* - Unknown
[2025-08-04T07:29:48.527Z] GET / - 200 - 5ms
[2025-08-04T07:30:14.330Z] GET / - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:14.331Z] GET / - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:14.333Z] GET / - 200 - 3ms
[2025-08-04T07:30:14.333Z] GET / - 200 - 2ms
[2025-08-04T07:30:14.361Z] GET /webui - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:14.363Z] GET /webui - 404 - 2ms
[2025-08-04T07:30:14.659Z] GET / - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:14.662Z] GET / - 200 - 3ms
[2025-08-04T07:30:14.744Z] GET /favicon.ico - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:14.745Z] GET /favicon.ico - 404 - 1ms
[2025-08-04T07:30:14.813Z] GET /owa/ - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:14.814Z] GET /owa/ - 404 - 1ms
[2025-08-04T07:30:15.189Z] GET /owa/ - ::ffff:************* - Mozilla/5.0 (iPhone; CPU iPhone OS 14_7 like Mac OS X) AppleWebKit/604.1.38 (KHTML
[2025-08-04T07:30:15.190Z] GET /owa/ - 404 - 1ms
[2025-08-04T07:30:34.497Z] GET / - ::ffff:************* - Unknown
[2025-08-04T07:30:34.498Z] GET / - 200 - 1ms
[2025-08-04T07:30:34.624Z] GET / - ::ffff:************* - Unknown
[2025-08-04T07:30:34.626Z] GET / - 200 - 2ms
[2025-08-04T07:43:03.245Z] GET / - ::ffff:************** - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.140 Safari/537.36
[2025-08-04T07:43:03.246Z] GET / - 200 - 1ms
[2025-08-04T07:44:28.321Z] POST /api/login - ::ffff:127.0.0.1 - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04T07:44:28.414Z] POST /api/login - 200 - 93ms
