# 🚀 简单配置步骤 - 不改变现有配置

## 📁 您的文件位置
```
/opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system
```

## ⚡ 3步快速配置

### 步骤1：启动Node.js应用
```bash
cd /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system

# 安装依赖
npm install

# 配置环境变量
cp config/.env.example .env
nano .env  # 编辑GOTONE_TOKEN

# 启动应用
nohup node server.js > logs/app.log 2>&1 &
```

### 步骤2：在1Panel中添加配置
1. 登录1Panel管理面板
2. 进入"网站" -> 找到您的 `dailuanshej.cn` 网站
3. 点击"配置" -> "配置文件"
4. **在现有配置的最后添加**（不要替换）`ADD-TO-1PANEL.conf` 文件中的内容
5. 保存配置
6. 重载Nginx

### 步骤3：测试访问
- 访问：https://api.dailuanshej.cn
- 应该看到短信系统登录页面

## 🔍 验证步骤

### 检查Node.js应用
```bash
# 检查进程是否运行
ps aux | grep node

# 检查端口是否监听
netstat -tlnp | grep :3000

# 检查应用日志
tail -f logs/app.log
```

### 检查本地访问
```bash
# 测试Node.js应用
curl http://localhost:3000

# 应该返回HTML内容
```

### 检查域名访问
```bash
# 测试域名解析
nslookup api.dailuanshej.cn

# 测试HTTP访问
curl -I http://api.dailuanshej.cn
```

## ⚠️ 重要提醒

1. **不要替换现有配置** - 只在现有nginx配置文件末尾添加新的server块
2. **保持现有网站正常** - 这个配置不会影响您的主站 dailuanshej.cn
3. **独立的子域名** - api.dailuanshej.cn 是独立的子域名配置

## 🛠️ 如果遇到问题

### 问题1：页面显示404
**检查**：
- Node.js应用是否启动：`ps aux | grep node`
- 端口是否监听：`netstat -tlnp | grep :3000`
- 1Panel配置是否保存并重载

### 问题2：CSS/JS加载失败
**检查**：
- public目录是否存在：`ls -la public/`
- 路径是否正确：确认root路径指向public目录

### 问题3：API请求失败
**检查**：
- Node.js应用日志：`tail -f logs/app.log`
- 代理配置是否正确：确认proxy_pass指向localhost:3000

## 📞 快速命令

```bash
# 重启Node.js应用
pkill -f "node.*server.js"
nohup node server.js > logs/app.log 2>&1 &

# 查看实时日志
tail -f logs/app.log

# 测试本地访问
curl http://localhost:3000/health
```

## 🎯 预期结果

配置成功后，访问 https://api.dailuanshej.cn 应该看到：
- ✅ 短信系统登录页面
- ✅ 注册/登录功能正常
- ✅ 页面样式正确显示
- ✅ API功能正常工作
