# 🚀 快速部署指南

## 域名配置 ✅
- **域名**: `api.dailuanshej.cn`
- **状态**: 已解析完成
- **访问**: https://api.dailuanshej.cn

## 📦 部署步骤

### 1. 上传文件到服务器
```bash
# 将整个 sms-system 文件夹上传到服务器
# 建议路径: /var/www/sms-system
```

### 2. 一键部署（推荐）
```bash
cd /var/www/sms-system
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 3. 配置环境变量
```bash
# 复制环境配置文件
cp config/.env.example .env

# 编辑配置文件
nano .env
```

**必须配置的环境变量**：
```env
PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-in-production
GOTONE_TOKEN=YOUR_GOTONE_API_TOKEN_HERE
NODE_ENV=production
```

### 4. 配置Nginx（推荐）
```bash
# 复制Nginx配置
sudo cp config/nginx.conf /etc/nginx/sites-available/sms-system
sudo ln -s /etc/nginx/sites-available/sms-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 5. 启动应用
```bash
# 方式一：使用PM2（推荐）
pm2 start config/ecosystem.config.js --env production
pm2 save

# 方式二：直接启动
npm start

# 方式三：后台运行
nohup node server.js > logs/app.log 2>&1 &
```

## 🔍 验证部署

### 检查服务状态
```bash
# 检查端口
netstat -tlnp | grep :3000

# 检查PM2状态
pm2 status

# 检查日志
tail -f logs/app.log
```

### 测试访问
1. **本地测试**: http://localhost:3000
2. **域名测试**: https://api.dailuanshej.cn
3. **API测试**: https://api.dailuanshej.cn/health

## 🛠️ 常用命令

```bash
# 重启应用
pm2 restart sms-system

# 停止应用
pm2 stop sms-system

# 查看日志
pm2 logs sms-system

# 查看监控
pm2 monit

# 重启Nginx
sudo systemctl restart nginx

# 查看Nginx状态
sudo systemctl status nginx
```

## 📱 功能测试

1. **注册用户**
   - 访问 https://api.dailuanshej.cn
   - 点击"注册"选项卡
   - 输入手机号和密码

2. **登录系统**
   - 使用注册的手机号和密码登录

3. **发送短信**
   - 单发：输入一个手机号和短信内容
   - 批量：输入多个手机号（换行分隔）
   - 导入：上传CSV或TXT文件

## ⚠️ 注意事项

1. **GoTone Token**: 必须配置有效的GoTone API Token
2. **防火墙**: 确保3000端口开放
3. **权限**: 确保应用有读写data/、uploads/、logs/目录的权限
4. **SSL证书**: 建议配置SSL证书启用HTTPS
5. **备份**: 定期备份data/users.json文件

## 🔧 故障排除

### 应用无法启动
```bash
# 检查依赖
npm install

# 检查配置文件
cat .env

# 检查端口占用
lsof -i :3000
```

### 无法访问域名
```bash
# 检查DNS解析
nslookup api.dailuanshej.cn

# 检查Nginx配置
sudo nginx -t

# 检查Nginx日志
sudo tail -f /var/log/nginx/error.log
```

### 短信发送失败
1. 检查GoTone Token是否正确
2. 检查网络连接
3. 查看应用日志：`tail -f logs/app.log`

## 📞 支持

如有问题，请检查：
1. 应用日志：`logs/app.log`
2. Nginx日志：`/var/log/nginx/sms-system-error.log`
3. PM2日志：`pm2 logs sms-system`
