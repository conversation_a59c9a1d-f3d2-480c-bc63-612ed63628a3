/* 管理后台样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: #667eea;
    font-size: 28px;
    margin-bottom: 5px;
}

.subtitle {
    color: #666;
    font-size: 14px;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 认证区域 */
.auth-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.auth-card h2 {
    color: #667eea;
    margin-bottom: 30px;
    font-size: 24px;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-group small {
    color: #666;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.auth-footer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.link {
    color: #667eea;
    text-decoration: none;
}

.link:hover {
    text-decoration: underline;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 管理主界面 */
.admin-main {
    display: flex;
    gap: 20px;
    min-height: 70vh;
}

/* 导航菜单 */
.admin-nav {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    width: 250px;
    height: fit-content;
}

.nav-btn {
    width: 100%;
    padding: 15px;
    margin-bottom: 10px;
    background: transparent;
    border: none;
    border-radius: 8px;
    text-align: left;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    color: #333;
}

.nav-btn:hover {
    background: #f8f9fa;
}

.nav-btn.active {
    background: #667eea;
    color: white;
}

/* 管理区域 */
.admin-section {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.admin-section h3 {
    color: #333;
    margin-bottom: 30px;
    font-size: 24px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

/* 仪表盘网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dashboard-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    text-align: center;
}

.dashboard-card h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

.status-display {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.stats-display {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.big-number {
    font-size: 36px;
    font-weight: bold;
    color: #667eea;
}

.unit {
    color: #666;
    font-size: 14px;
}

/* 控制网格 */
.control-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.control-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
    text-align: center;
}

.control-card h4 {
    color: #333;
    margin-bottom: 10px;
}

.control-card p {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

/* 令牌卡片 */
.token-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.token-card h4 {
    color: #333;
    margin-bottom: 25px;
    font-size: 20px;
}

/* 用户管理 */
.user-management {
    max-height: 600px;
    overflow-y: auto;
}

.user-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.user-list {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    max-height: 500px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.user-info {
    flex: 1;
}

.user-actions {
    display: flex;
    gap: 5px;
}

.user-actions .btn {
    padding: 8px 12px;
    font-size: 12px;
}

/* 日志容器 */
.logs-container {
    max-height: 600px;
}

.logs-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.logs-content {
    background: #1e1e1e;
    color: #f8f8f2;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* 统计容器 */
.stats-container {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

/* 加载和消息样式 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: white;
    margin-top: 15px;
    font-size: 16px;
}

.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #28a745;
}

.message.error {
    background: #dc3545;
}

.message.info {
    background: #17a2b8;
}

.message.warning {
    background: #ffc107;
    color: #212529;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-main {
        flex-direction: column;
    }
    
    .admin-nav {
        width: 100%;
        display: flex;
        overflow-x: auto;
        padding: 15px;
    }
    
    .nav-btn {
        min-width: 150px;
        margin-right: 10px;
        margin-bottom: 0;
    }
    
    .dashboard-grid,
    .control-grid {
        grid-template-columns: 1fr;
    }
    
    .user-controls,
    .logs-controls {
        flex-direction: column;
    }
}
