// 数据库操作工具类
const fs = require('fs');
const path = require('path');
const config = require('../config/config');

class Database {
  constructor() {
    this.usersFile = config.database.usersFile;
    this.dataDir = path.dirname(this.usersFile);
    this.initializeDatabase();
  }

  // 初始化数据库
  initializeDatabase() {
    // 确保数据目录存在
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }

    // 初始化用户数据文件
    if (!fs.existsSync(this.usersFile)) {
      this.writeUsers([]);
    }
  }

  // 读取用户数据
  readUsers() {
    try {
      const data = fs.readFileSync(this.usersFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('读取用户数据失败:', error);
      return [];
    }
  }

  // 写入用户数据
  writeUsers(users) {
    try {
      fs.writeFileSync(this.usersFile, JSON.stringify(users, null, 2));
      return true;
    } catch (error) {
      console.error('写入用户数据失败:', error);
      return false;
    }
  }

  // 根据手机号查找用户
  findUserByPhone(phone) {
    const users = this.readUsers();
    return users.find(user => user.phone === phone);
  }

  // 根据ID查找用户
  findUserById(id) {
    const users = this.readUsers();
    return users.find(user => user.id === id);
  }

  // 创建新用户
  createUser(userData) {
    const users = this.readUsers();
    
    // 检查用户是否已存在
    if (this.findUserByPhone(userData.phone)) {
      throw new Error('手机号已注册');
    }

    // 创建新用户
    const newUser = {
      id: Date.now().toString(),
      phone: userData.phone,
      username: userData.username || userData.phone,
      password: userData.password,
      createdAt: new Date().toISOString(),
      lastLoginAt: null
    };

    users.push(newUser);
    
    if (this.writeUsers(users)) {
      return newUser;
    } else {
      throw new Error('用户创建失败');
    }
  }

  // 更新用户信息
  updateUser(id, updateData) {
    const users = this.readUsers();
    const userIndex = users.findIndex(user => user.id === id);
    
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }

    // 更新用户数据
    users[userIndex] = {
      ...users[userIndex],
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    if (this.writeUsers(users)) {
      return users[userIndex];
    } else {
      throw new Error('用户更新失败');
    }
  }

  // 更新用户最后登录时间
  updateLastLogin(id) {
    return this.updateUser(id, {
      lastLoginAt: new Date().toISOString()
    });
  }

  // 删除用户
  deleteUser(id) {
    const users = this.readUsers();
    const filteredUsers = users.filter(user => user.id !== id);
    
    if (filteredUsers.length === users.length) {
      throw new Error('用户不存在');
    }

    return this.writeUsers(filteredUsers);
  }

  // 获取用户统计信息
  getUserStats() {
    const users = this.readUsers();
    return {
      totalUsers: users.length,
      recentUsers: users.filter(user => {
        const createdAt = new Date(user.createdAt);
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return createdAt > weekAgo;
      }).length
    };
  }
}

module.exports = new Database();
