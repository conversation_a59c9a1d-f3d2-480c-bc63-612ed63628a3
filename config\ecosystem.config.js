// PM2 配置文件
module.exports = {
  apps: [{
    name: 'sms-system',
    script: '../server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '../logs/err.log',
    out_file: '../logs/out.log',
    log_file: '../logs/combined.log',
    time: true,
    // 自动重启配置
    min_uptime: '10s',
    max_restarts: 10,
    // 内存监控
    max_memory_restart: '500M',
    // 集群模式配置
    exec_mode: 'fork',
    // 环境变量文件
    env_file: '../.env'
  }]
};
