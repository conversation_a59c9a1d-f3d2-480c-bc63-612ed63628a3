# GoTone SMS API 对接文档

## 概述
GoTone SMS API 允许您通过 REST API 向世界任何国家发送和接收短信。每条消息都由唯一的随机ID标识，用户可以随时使用给定的端点检查消息状态。

## 基础配置

### API 基础信息
- **基础URL**: `https://gotones.site/api/v3`
- **API Token**: `67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc`
- **认证方式**: Bearer Token
- **内容类型**: `application/json`

### 请求头要求
```http
Authorization: Bearer 67639|JUjQwhrqKAPzxFsIgQzbbMcfxFjR8JhflFNYUcpae5524dc
Content-Type: application/json
Accept: application/json
```

## 发送短信 API

### 端点
```
POST https://gotones.site/api/v3/sms/send
```

### 必填参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `recipient` | string | 是 | 接收号码。使用逗号(,)分隔多个号码。例：31612345678,8801721970168 |
| `sender_id` | string | 是 | 发送方标识。可以是电话号码(包含国家代码)或字母数字字符串(最大11字符) |
| `type` | string | 是 | 消息类型。文本消息使用 `plain` |
| `message` | string | 是 | 短信内容 |

### 可选参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `schedule_time` | datetime | 否 | 定时发送时间，RFC3339格式 (Y-m-d H:i) |
| `dlt_template_id` | string | 否 | DLT内容模板ID |

### 单个号码发送示例
```bash
curl -X POST https://gotones.site/api/v3/sms/send \
-H 'Authorization: Bearer 67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc' \
-H 'Content-Type: application/json' \
-H 'Accept: application/json' \
-d '{
    "recipient":"31612345678",
    "sender_id":"YourName",
    "type":"plain",
    "message":"Your verification code is 123456."
}'
```

### 多个号码发送示例
```bash
curl -X POST https://gotones.site/api/v3/sms/send \
-H 'Authorization: Bearer 67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc' \
-H 'Content-Type: application/json' \
-H 'Accept: application/json' \
-d '{
    "recipient":"31612345678,880172145789",
    "sender_id":"YourName",
    "type":"plain",
    "message":"Your verification code is 123456.",
    "schedule_time":"2021-12-20 07:00"
}'
```

### JavaScript 发送示例
```javascript
async function sendSMS(recipients, message, senderId = 'YourName') {
    const GOTONE_API_URL = 'https://gotones.site/api/v3/sms/send';
    const GOTONE_API_TOKEN = '67639|JUjQwhrqKAPzxFsIgQzbbMcfvxFjR8JhflFNYUcpae5524dc';
    
    // 处理接收号码格式
    const recipientList = Array.isArray(recipients) 
        ? recipients.join(',') 
        : recipients.toString();
    
    const requestData = {
        recipient: recipientList,
        sender_id: senderId,
        type: 'plain',
        message: message
    };
    
    try {
        const response = await fetch(GOTONE_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${GOTONE_API_TOKEN}`
            },
            body: JSON.stringify(requestData)
        });
        
        const data = await response.json();
        
        if (response.ok && data.status === 'success') {
            console.log('发送成功:', data);
            return { success: true, data: data };
        } else {
            console.error('发送失败:', data.message);
            return { success: false, error: data.message };
        }
    } catch (error) {
        console.error('网络错误:', error);
        return { success: false, error: error.message };
    }
}
```

## 响应格式

### 成功响应
```json
{
    "status": "success",
    "data": "sms reports with all details"
}
```

### 失败响应
```json
{
    "status": "error",
    "message": "A human-readable description of the error."
}
```

## 批量发送 (Campaign API)

### 端点
```
POST https://gotones.site/api/v3/sms/campaign
```

### 参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `contact_list_id` | string | 是 | 联系人列表ID。使用逗号分隔多个列表 |
| `sender_id` | string | 是 | 发送方标识 |
| `type` | string | 是 | 消息类型 (`plain`) |
| `message` | string | 是 | 短信内容 |
| `schedule_time` | datetime | 否 | 定时发送时间 |
| `dlt_template_id` | string | 否 | DLT模板ID |

## 查看短信状态

### 查看单条短信
```
GET https://gotones.site/api/v3/sms/{uid}
```

### 查看所有短信
```
GET https://gotones.site/api/v3/sms/
```

## 错误处理

### 常见错误码
- **401 Unauthorized**: API Token无效或过期
- **400 Bad Request**: 请求参数错误
- **429 Too Many Requests**: 请求频率超限
- **500 Internal Server Error**: 服务器内部错误

### 错误处理示例
```javascript
if (!response.ok) {
    switch (response.status) {
        case 401:
            throw new Error('API Token无效，请检查认证信息');
        case 400:
            throw new Error('请求参数错误，请检查发送内容');
        case 429:
            throw new Error('请求过于频繁，请稍后重试');
        default:
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
}
```

## 最佳实践

### 1. 号码格式
- 使用国际格式，包含国家代码
- 中国号码示例：`8613800138000`
- 多个号码用逗号分隔：`8613800138000,8613800138001`

### 2. 发送方ID
- 使用有意义的标识符
- 字母数字字符串最大11字符
- 避免使用特殊字符

### 3. 消息内容
- 保持简洁明了
- 避免敏感词汇
- 注意字符编码

### 4. 错误重试
- 实现指数退避重试机制
- 记录失败日志
- 设置最大重试次数

## 注意事项
- 请妥善保管API Token，不要在客户端代码中暴露敏感信息
- 在生产环境中，建议通过后端服务器代理API调用
- 遵守当地法律法规和短信发送规范
