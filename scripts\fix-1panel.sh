#!/bin/bash

# 1Panel环境快速修复脚本
echo "🔧 1Panel + OpenResty 环境快速修复"
echo "=================================="

# 检查当前路径
CURRENT_PATH=$(pwd)
echo "📁 当前路径: $CURRENT_PATH"

# 检查是否在正确的目录
if [[ $CURRENT_PATH == *"sms-system"* ]]; then
    echo "✅ 在sms-system目录中"
else
    echo "❌ 请确保在sms-system目录中运行此脚本"
    exit 1
fi

# 1. 检查文件结构
echo ""
echo "📂 检查文件结构..."
if [ -f "server.js" ] && [ -d "public" ] && [ -d "src" ]; then
    echo "✅ 文件结构正确"
else
    echo "❌ 文件结构不完整"
    exit 1
fi

# 2. 安装依赖
echo ""
echo "📦 安装Node.js依赖..."
if command -v npm &> /dev/null; then
    npm install
    echo "✅ 依赖安装完成"
else
    echo "❌ npm未安装，请先安装Node.js"
    exit 1
fi

# 3. 创建必要目录
echo ""
echo "📁 创建必要目录..."
mkdir -p data logs uploads
echo "✅ 目录创建完成"

# 4. 配置环境变量
echo ""
echo "⚙️ 配置环境变量..."
if [ ! -f ".env" ]; then
    cp config/.env.example .env
    echo "✅ .env文件已创建"
    echo "⚠️  请编辑.env文件配置GOTONE_TOKEN"
else
    echo "ℹ️  .env文件已存在"
fi

# 5. 检查端口占用
echo ""
echo "🔌 检查端口3000..."
if netstat -tlnp 2>/dev/null | grep :3000 &> /dev/null; then
    echo "⚠️  端口3000已被占用"
    echo "   占用进程: $(netstat -tlnp 2>/dev/null | grep :3000 | awk '{print $7}')"
    echo "   如需停止: pkill -f 'node.*server.js'"
else
    echo "✅ 端口3000可用"
fi

# 6. 启动应用
echo ""
echo "🚀 启动应用..."

# 检查PM2
if command -v pm2 &> /dev/null; then
    echo "使用PM2启动..."
    pm2 start config/ecosystem.config.js --env production
    pm2 save
    echo "✅ PM2启动完成"
else
    echo "使用nohup启动..."
    nohup node server.js > logs/app.log 2>&1 &
    echo "✅ 后台启动完成"
fi

# 7. 等待应用启动
echo ""
echo "⏳ 等待应用启动..."
sleep 3

# 8. 测试应用
echo ""
echo "🧪 测试应用..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Node.js应用运行正常"
else
    echo "❌ Node.js应用启动失败"
    echo "   请检查日志: tail -f logs/app.log"
fi

# 9. 提示Nginx配置
echo ""
echo "🌐 Nginx配置说明..."
echo "⚠️  请不要替换现有配置，只需添加新配置"
echo "📄 配置文件已准备：ADD-TO-1PANEL.conf"
echo ""
echo "请将 ADD-TO-1PANEL.conf 中的内容添加到1Panel现有配置的末尾"


# 10. 显示结果
echo ""
echo "🎉 修复完成！"
echo "=================================="
echo ""
echo "📋 接下来的步骤:"
echo "1. 编辑 .env 文件配置 GOTONE_TOKEN"
echo "2. 在1Panel中添加 ADD-TO-1PANEL.conf 的配置（不要替换现有配置）"
echo "3. 保存并重载Nginx配置"
echo "4. 访问 https://api.dailuanshej.cn 测试"
echo ""
echo "⚠️  重要：只添加配置，不要替换现有的1Panel配置！"
echo ""
echo "🔍 检查命令:"
echo "- 查看应用状态: pm2 status 或 ps aux | grep node"
echo "- 查看应用日志: tail -f logs/app.log"
echo "- 测试本地访问: curl http://localhost:3000"
echo ""
echo "📞 如有问题，请查看 1PANEL-SETUP.md 详细指南"
