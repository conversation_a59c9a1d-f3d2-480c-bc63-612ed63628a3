// 管理后台JavaScript
let adminToken = null;
let currentAdmin = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    adminToken = localStorage.getItem('adminToken');
    if (adminToken) {
        // 验证token是否有效
        verifyAdminToken();
    } else {
        showAdminAuth();
    }

    // 设置事件监听器
    setupEventListeners();
});

// 验证管理员token
async function verifyAdminToken() {
    if (!adminToken) {
        console.log('没有token，显示登录页面');
        showAdminAuth();
        return;
    }

    console.log('验证管理员token:', adminToken.substring(0, 20) + '...');

    try {
        const response = await fetch('/api/admin/status', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        console.log('token验证响应状态:', response.status);

        if (response.ok) {
            const data = await response.json();
            console.log('token验证成功:', data);
            showAdminMain();
            loadAllData();
        } else {
            console.log('token验证失败，清除token');
            // Token无效，清除并显示登录页面
            localStorage.removeItem('adminToken');
            adminToken = null;
            showAdminAuth();
        }
    } catch (error) {
        console.error('token验证网络错误:', error);
        // 网络错误时也清除token，避免使用无效token
        localStorage.removeItem('adminToken');
        adminToken = null;
        showAdminAuth();
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 管理员登录表单
    document.getElementById('adminLoginForm').addEventListener('submit', handleAdminLogin);
}

// 显示管理员认证区域
function showAdminAuth() {
    document.getElementById('adminAuthSection').style.display = 'flex';
    document.getElementById('adminMainSection').style.display = 'none';
    document.getElementById('adminInfo').style.display = 'none';
}

// 显示管理主界面
function showAdminMain() {
    document.getElementById('adminAuthSection').style.display = 'none';
    document.getElementById('adminMainSection').style.display = 'flex';
    document.getElementById('adminInfo').style.display = 'flex';
    if (currentAdmin) {
        document.getElementById('adminName').textContent = currentAdmin.username || '管理员';
    }
}

// 处理管理员登录
async function handleAdminLogin(e) {
    e.preventDefault();

    const username = document.getElementById('adminUsername').value;
    const password = document.getElementById('adminPassword').value;

    if (!username || !password) {
        showMessage('请输入管理员账号和密码', 'error');
        return;
    }

    showLoading();

    try {
        const response = await fetch('/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            adminToken = data.token;
            currentAdmin = data.admin;
            localStorage.setItem('adminToken', adminToken);
            showMessage('登录成功', 'success');
            showAdminMain();
            loadAllData();
        } else {
            showMessage(data.error || '登录失败', 'error');
        }
    } catch (error) {
        showMessage('服务器连接失败，使用离线模式', 'warning');
        // 离线模式登录
        if (username === 'admin' && password === 'admin123') {
            adminToken = 'offline_admin_token_' + Date.now();
            currentAdmin = { username: username };
            localStorage.setItem('adminToken', adminToken);
            showAdminMain();
            loadOfflineData();
        } else {
            showMessage('离线模式下请使用默认账号：admin/admin123', 'error');
        }
    } finally {
        hideLoading();
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('adminToken');
    adminToken = null;
    currentAdmin = null;
    showMessage('已退出登录', 'info');
    showAdminAuth();

    // 清空表单
    document.getElementById('adminLoginForm').reset();
}

// ==================== 导航功能 ====================

// 显示仪表盘
function showDashboard() {
    hideAllSections();
    document.getElementById('dashboardSection').style.display = 'block';
    updateNavActive(0);
    loadDashboardData();
}

// 显示系统控制
function showSystemControl() {
    hideAllSections();
    document.getElementById('systemControlSection').style.display = 'block';
    updateNavActive(1);
}

// 显示令牌管理
function showTokenManage() {
    hideAllSections();
    document.getElementById('tokenManageSection').style.display = 'block';
    updateNavActive(2);
    loadCurrentToken();
}

// 显示用户管理
function showUserManage() {
    hideAllSections();
    document.getElementById('userManageSection').style.display = 'block';
    updateNavActive(3);
    refreshUsers();
}

// 显示日志管理
function showLogs() {
    hideAllSections();
    document.getElementById('logsSection').style.display = 'block';
    updateNavActive(4);
    refreshLogs();
}

// 显示统计分析
function showStats() {
    hideAllSections();
    document.getElementById('statsSection').style.display = 'block';
    updateNavActive(5);
    loadDetailedStats();
}

// 隐藏所有区域
function hideAllSections() {
    const sections = document.querySelectorAll('.admin-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
}

// 更新导航激活状态
function updateNavActive(activeIndex) {
    const navBtns = document.querySelectorAll('.nav-btn');
    navBtns.forEach((btn, index) => {
        if (index === activeIndex) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

// ==================== 数据加载功能 ====================

// 加载所有数据
async function loadAllData() {
    try {
        console.log('开始加载管理数据...');
        await loadDashboardData();
        await loadCurrentToken();
        console.log('管理数据加载完成');
    } catch (error) {
        console.error('API加载失败:', error);
        showMessage('部分数据加载失败，使用离线模式', 'warning');
        loadOfflineData();
    }
}

// 离线模式数据
function loadOfflineData() {
    // 显示离线状态
    document.getElementById('systemStatus').innerHTML = `
        <p><strong>状态:</strong> 离线模式</p>
        <p><strong>说明:</strong> 无法连接到服务器</p>
        <p><strong>功能:</strong> 部分功能不可用</p>
    `;

    document.getElementById('todayStats').innerHTML = `
        <span class="big-number">--</span>
        <span class="unit">离线模式</span>
    `;

    document.getElementById('userCount').innerHTML = `
        <span class="big-number">--</span>
        <span class="unit">离线模式</span>
    `;

    document.getElementById('successRate').innerHTML = `
        <span class="big-number">--</span>
        <span class="unit">离线模式</span>
    `;

    if (document.getElementById('currentToken')) {
        document.getElementById('currentToken').value = '离线模式 - 无法获取';
    }
}

// 加载仪表盘数据
async function loadDashboardData() {
    try {
        // 检查是否为离线模式（只有temp_开头的才是离线）
        if (adminToken && adminToken.includes('temp_admin_token_')) {
            loadOfflineData();
            return;
        }

        // 加载系统状态
        const statusResponse = await fetch('/api/admin/status', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (statusResponse.ok) {
            const statusData = await statusResponse.json();
            document.getElementById('systemStatus').innerHTML = `
                <p><strong>状态:</strong> ${statusData.status}</p>
                <p><strong>运行时间:</strong> ${formatUptime(statusData.uptime)}</p>
                <p><strong>内存:</strong> ${statusData.memoryUsage}</p>
                <p><strong>Node.js:</strong> ${statusData.nodeVersion}</p>
            `;
        } else {
            throw new Error('API响应失败');
        }

        // 加载统计数据
        const statsResponse = await fetch('/api/admin/stats', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            document.getElementById('todayStats').innerHTML = `
                <span class="big-number">${statsData.todaySent || 0}</span>
                <span class="unit">条短信</span>
            `;
            document.getElementById('successRate').innerHTML = `
                <span class="big-number">${statsData.successRate || 100}</span>
                <span class="unit">%</span>
            `;
        }

        // 加载用户数量
        const usersResponse = await fetch('/api/admin/users', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (usersResponse.ok) {
            const usersData = await usersResponse.json();
            document.getElementById('userCount').innerHTML = `
                <span class="big-number">${usersData.users.length}</span>
                <span class="unit">个用户</span>
            `;
        }

    } catch (error) {
        console.error('加载仪表盘数据失败:', error);
        loadOfflineData();
    }
}

// 刷新所有数据
async function refreshAllData() {
    showLoading();
    try {
        await loadAllData();
        showMessage('数据刷新成功', 'success');
    } catch (error) {
        showMessage('数据刷新失败', 'error');
    } finally {
        hideLoading();
    }
}

// ==================== 工具函数 ====================

// 显示加载提示
function showLoading() {
    document.getElementById('loading').style.display = 'flex';
}

// 隐藏加载提示
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// 显示消息
function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('message');
    messageEl.textContent = message;
    messageEl.className = `message ${type}`;
    messageEl.style.display = 'block';

    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}

// 格式化运行时间
function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
        return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
        return `${hours}小时 ${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}


// ==================== 管理功能实现 ====================

// 加载当前令牌
async function loadCurrentToken() {
    try {
        // 检查是否为离线模式（只有temp_开头的才是离线）
        if (adminToken && adminToken.includes('temp_admin_token_')) {
            const tokenEl = document.getElementById('currentToken');
            if (tokenEl) tokenEl.value = '离线模式 - 无法获取';
            return;
        }

        const response = await fetch('/api/admin/token', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();

        if (response.ok) {
            const maskedToken = data.token ? data.token.substring(0, 10) + '...' + data.token.slice(-10) : '未设置';
            const tokenEl = document.getElementById('currentToken');
            if (tokenEl) tokenEl.value = maskedToken;
        } else {
            throw new Error('API响应失败');
        }
    } catch (error) {
        console.error('加载令牌失败:', error);
        const tokenEl = document.getElementById('currentToken');
        if (tokenEl) tokenEl.value = '获取失败';
    }
}

// 更新API令牌
async function updateApiToken() {
    const newToken = document.getElementById('newToken').value.trim();

    if (!newToken) {
        showMessage('请输入新的API令牌', 'error');
        return;
    }

    if (!confirm('确定要更新API令牌吗？这将重启系统。')) {
        return;
    }

    showLoading();

    try {
        const response = await fetch('/api/admin/update-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${adminToken}`
            },
            body: JSON.stringify({ token: newToken })
        });

        const data = await response.json();

        if (response.ok) {
            showMessage('API令牌更新成功，系统正在重启...', 'success');
            document.getElementById('newToken').value = '';
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            showMessage(data.error || 'API令牌更新失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    } finally {
        hideLoading();
    }
}

// 重启系统
async function restartSystem() {
    if (!confirm('确定要重启系统吗？这将中断所有正在进行的操作。')) {
        return;
    }

    showLoading();

    try {
        const response = await fetch('/api/admin/restart', {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (response.ok) {
            showMessage('系统正在重启，请稍候...', 'info');
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        } else {
            showMessage('重启失败', 'error');
        }
    } catch (error) {
        showMessage('重启请求发送失败', 'error');
    } finally {
        hideLoading();
    }
}

// 清空日志
async function clearLogs() {
    if (!confirm('确定要清空所有日志吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch('/api/admin/clear-logs', {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();

        if (response.ok) {
            showMessage('日志清空成功', 'success');
            if (document.getElementById('logsContent')) {
                document.getElementById('logsContent').textContent = '日志已清空';
            }
        } else {
            showMessage(data.error || '日志清空失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    }
}

// 导出日志
async function exportLogs() {
    try {
        const response = await fetch('/api/admin/export-logs', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sms-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showMessage('日志导出成功', 'success');
        } else {
            showMessage('日志导出失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    }
}

// 刷新用户列表
async function refreshUsers() {
    try {
        const response = await fetch('/api/admin/users', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();

        if (response.ok) {
            const userListHtml = data.users.map(user => `
                <div class="user-item">
                    <div class="user-info">
                        <strong>${user.username || user.phone}</strong><br>
                        <small>📱 ${user.phone} | 📅 ${new Date(user.createdAt).toLocaleDateString()}</small>
                    </div>
                    <div class="user-actions">
                        <button onclick="deleteUser('${user.phone}')" class="btn btn-danger">删除</button>
                    </div>
                </div>
            `).join('');

            document.getElementById('userList').innerHTML = userListHtml || '<p>暂无用户</p>';
        } else {
            document.getElementById('userList').innerHTML = '<p>❌ 无法获取用户列表</p>';
        }
    } catch (error) {
        document.getElementById('userList').innerHTML = '<p>❌ 用户列表获取失败</p>';
    }
}

// 删除用户
async function deleteUser(phone) {
    if (!confirm(`确定要删除用户 ${phone} 吗？此操作不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/users/${phone}`, {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();

        if (response.ok) {
            showMessage('用户删除成功', 'success');
            refreshUsers();
        } else {
            showMessage(data.error || '用户删除失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    }
}

// 导出用户
async function exportUsers() {
    try {
        const response = await fetch('/api/admin/users', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();

        if (response.ok) {
            const csvContent = 'data:text/csv;charset=utf-8,' +
                '手机号,用户名,注册时间\n' +
                data.users.map(user =>
                    `${user.phone},${user.username || ''},${new Date(user.createdAt).toLocaleString()}`
                ).join('\n');

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', `users-${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showMessage('用户数据导出成功', 'success');
        } else {
            showMessage('用户数据导出失败', 'error');
        }
    } catch (error) {
        showMessage('网络错误，请重试', 'error');
    }
}

// 刷新日志
async function refreshLogs() {
    try {
        const response = await fetch('/api/admin/export-logs', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        if (response.ok) {
            const logContent = await response.text();
            const lines = logContent.split('\n');
            const recentLines = lines.slice(-100); // 显示最近100行
            document.getElementById('logsContent').textContent = recentLines.join('\n');
        } else {
            document.getElementById('logsContent').textContent = '无法获取日志内容';
        }
    } catch (error) {
        document.getElementById('logsContent').textContent = '日志获取失败';
    }
}

// 加载详细统计
async function loadDetailedStats() {
    try {
        const response = await fetch('/api/admin/stats', {
            headers: { 'Authorization': `Bearer ${adminToken}` }
        });

        const data = await response.json();

        if (response.ok) {
            document.getElementById('detailedStats').innerHTML = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <h4>📊 发送统计</h4>
                        <p>今日发送: ${data.todaySent || 0} 条</p>
                        <p>本月发送: ${data.monthSent || 0} 条</p>
                        <p>总发送量: ${data.totalSent || 0} 条</p>
                        <p>成功率: ${data.successRate || 100}%</p>
                    </div>
                    <div class="stat-item">
                        <h4>🕐 时间信息</h4>
                        <p>最后发送: ${data.lastSent ? new Date(data.lastSent).toLocaleString() : '无'}</p>
                        <p>系统启动: ${new Date().toLocaleString()}</p>
                    </div>
                </div>
            `;
        } else {
            document.getElementById('detailedStats').innerHTML = '<p>❌ 无法获取统计数据</p>';
        }
    } catch (error) {
        document.getElementById('detailedStats').innerHTML = '<p>❌ 统计数据获取失败</p>';
    }
}
// 初始化时显示仪表盘
setTimeout(() => {
    if (adminToken) {
        showDashboard();
    }
}, 100);
