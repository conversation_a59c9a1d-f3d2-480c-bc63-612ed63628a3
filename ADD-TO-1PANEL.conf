# ========================================
# 请将以下配置添加到您的1Panel现有配置中
# 不要替换现有配置，只需要添加这个server块
# ========================================

# SMS系统子域名配置 - 添加到现有nginx配置中
server {
    listen 80;
    server_name api.dailuanshej.cn;
    
    # 静态文件根目录 - 您的实际路径
    root /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system/public;
    index index.html;
    
    # 静态文件直接服务
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # API请求代理到Node.js
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 主页面和其他请求
    location / {
        try_files $uri $uri/ @nodejs;
    }
    
    # 回退到Node.js处理
    location @nodejs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # 访问日志（可选）
    access_log /opt/1panel/apps/openresty/openresty/logs/api-dailuanshej-access.log;
    error_log /opt/1panel/apps/openresty/openresty/logs/api-dailuanshej-error.log;
}

# ========================================
# 配置说明：
# 1. 这是一个独立的server块，添加到现有配置即可
# 2. 不会影响您现有的dailuanshej.cn主站配置
# 3. 专门处理api.dailuanshej.cn子域名
# 4. 静态文件直接由nginx服务
# 5. API请求代理到Node.js应用（端口3000）
# ========================================
