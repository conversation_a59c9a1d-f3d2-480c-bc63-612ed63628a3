// 管理路由
const express = require('express');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
// const bcrypt = require('bcryptjs');
// const jwt = require('jsonwebtoken');
const router = express.Router();

const { authenticateToken } = require('../middleware/auth');

// 管理员session存储
const adminSessions = new Map();

// 清理过期的管理员session
setInterval(() => {
  const now = Date.now();
  for (const [sessionId, session] of adminSessions.entries()) {
    if (now - session.createdAt > 24 * 60 * 60 * 1000) { // 24小时过期
      adminSessions.delete(sessionId);
      console.log('清理过期管理员session:', sessionId);
    }
  }
}, 60 * 60 * 1000); // 每小时清理一次

// 管理员认证中间件
function authenticateAdmin(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    console.log('管理员认证请求:', req.url);
    console.log('管理员token:', token ? token.substring(0, 20) + '...' : 'null');

    if (!token) {
        return res.status(401).json({ error: '需要管理员权限' });
    }

    // 检查服务器端管理员session
    if (adminSessions.has(token)) {
        const session = adminSessions.get(token);
        console.log('使用管理员session:', session.admin.username);
        req.admin = session.admin;
        // 更新最后访问时间
        session.lastAccess = Date.now();
        return next();
    }

    // 兼容旧的token格式
    if (token.startsWith('admin_token_') || token.startsWith('admin_session_')) {
        req.admin = { username: 'admin' };
        next();
    } else {
        console.log('无效的管理员token');
        res.status(403).json({ error: '无效的管理员token' });
    }
}
const config = require('../config/config');

// 默认管理员账号（可以通过环境变量配置）
const ADMIN_USERNAME = process.env.ADMIN_USERNAME || 'admin';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';

// 管理员登录
router.post('/login', async (req, res) => {
    try {
        console.log('管理员登录请求:', req.body);
        const { username, password } = req.body;

        if (!username || !password) {
            console.log('缺少用户名或密码');
            return res.status(400).json({ error: '请输入用户名和密码' });
        }

        console.log(`验证账号: "${username}", 期望: "${ADMIN_USERNAME}"`);
        console.log(`验证密码: "${password}", 期望: "${ADMIN_PASSWORD}"`);
        console.log(`账号匹配: ${username === ADMIN_USERNAME}`);
        console.log(`密码匹配: ${password === ADMIN_PASSWORD}`);

        // 验证管理员账号
        if (username !== ADMIN_USERNAME || password !== ADMIN_PASSWORD) {
            console.log('账号或密码错误');
            return res.status(401).json({ error: '管理员账号或密码错误' });
        }

        // 创建服务器端管理员session
        const sessionId = 'admin_session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        adminSessions.set(sessionId, {
            admin: { username: username },
            createdAt: Date.now(),
            lastAccess: Date.now()
        });

        console.log('创建管理员session:', sessionId);

        res.json({
            message: '登录成功',
            token: sessionId,
            admin: { username: username }
        });

    } catch (error) {
        console.error('管理员登录错误:', error);
        res.status(500).json({ error: '登录失败', details: error.message });
    }
});

// 系统启动时间
const startTime = new Date();

// 获取系统状态
router.get('/status', authenticateAdmin, (req, res) => {
    try {
        const uptime = process.uptime();
        const memoryUsage = process.memoryUsage();
        const memoryMB = Math.round(memoryUsage.rss / 1024 / 1024);
        
        res.json({
            status: 'running',
            uptime: uptime,
            nodeVersion: process.version,
            memoryUsage: `${memoryMB} MB`,
            startTime: startTime.toISOString(),
            environment: process.env.NODE_ENV || 'development'
        });
    } catch (error) {
        res.status(500).json({ error: '获取系统状态失败' });
    }
});

// 获取发送统计
router.get('/stats', authenticateAdmin, (req, res) => {
    try {
        // 这里可以从日志文件或数据库中获取统计数据
        // 暂时返回模拟数据
        const stats = {
            todaySent: 0,
            monthSent: 0,
            totalSent: 0,
            successRate: 100,
            lastSent: null
        };
        
        // 尝试从日志文件中获取实际数据
        const logPath = path.join(__dirname, '../../logs/app.log');
        if (fs.existsSync(logPath)) {
            const logContent = fs.readFileSync(logPath, 'utf8');
            const smsLines = logContent.split('\n').filter(line => 
                line.includes('SMS发送') || line.includes('短信发送')
            );
            stats.totalSent = smsLines.length;
        }
        
        res.json(stats);
    } catch (error) {
        res.status(500).json({ error: '获取统计数据失败' });
    }
});

// 获取用户列表
router.get('/users', authenticateAdmin, (req, res) => {
    try {
        const usersPath = path.join(__dirname, '../../data/users.json');
        
        if (!fs.existsSync(usersPath)) {
            return res.json({ users: [] });
        }
        
        const usersData = fs.readFileSync(usersPath, 'utf8');
        const users = JSON.parse(usersData);
        
        // 移除敏感信息
        const safeUsers = users.map(user => ({
            phone: user.phone,
            username: user.username,
            createdAt: user.createdAt || new Date().toISOString()
        }));
        
        res.json({ users: safeUsers });
    } catch (error) {
        res.status(500).json({ error: '获取用户列表失败' });
    }
});

// 获取当前API令牌
router.get('/token', authenticateAdmin, (req, res) => {
    try {
        const token = process.env.GOTONE_TOKEN || '';
        res.json({ token: token });
    } catch (error) {
        res.status(500).json({ error: '获取令牌失败' });
    }
});

// 更新API令牌
router.post('/update-token', authenticateAdmin, (req, res) => {
    try {
        const { token } = req.body;
        
        if (!token) {
            return res.status(400).json({ error: '令牌不能为空' });
        }
        
        // 更新.env文件
        const envPath = path.join(__dirname, '../../.env');
        let envContent = '';
        
        if (fs.existsSync(envPath)) {
            envContent = fs.readFileSync(envPath, 'utf8');
        }
        
        // 更新或添加GOTONE_TOKEN
        const lines = envContent.split('\n');
        let tokenUpdated = false;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith('GOTONE_TOKEN=')) {
                lines[i] = `GOTONE_TOKEN=${token}`;
                tokenUpdated = true;
                break;
            }
        }
        
        if (!tokenUpdated) {
            lines.push(`GOTONE_TOKEN=${token}`);
        }
        
        fs.writeFileSync(envPath, lines.join('\n'));
        
        // 更新环境变量
        process.env.GOTONE_TOKEN = token;
        
        res.json({ message: 'API令牌更新成功' });
        
        // 延迟重启以确保响应发送
        setTimeout(() => {
            process.exit(0);
        }, 1000);
        
    } catch (error) {
        res.status(500).json({ error: '更新令牌失败' });
    }
});

// 重启系统
router.post('/restart', authenticateAdmin, (req, res) => {
    try {
        res.json({ message: '系统正在重启' });
        
        // 延迟重启以确保响应发送
        setTimeout(() => {
            process.exit(0);
        }, 1000);
        
    } catch (error) {
        res.status(500).json({ error: '重启失败' });
    }
});

// 清空日志
router.post('/clear-logs', authenticateAdmin, (req, res) => {
    try {
        const logPath = path.join(__dirname, '../../logs/app.log');
        
        if (fs.existsSync(logPath)) {
            fs.writeFileSync(logPath, '');
        }
        
        res.json({ message: '日志清空成功' });
    } catch (error) {
        res.status(500).json({ error: '清空日志失败' });
    }
});

// 导出日志
router.get('/export-logs', authenticateAdmin, (req, res) => {
    try {
        const logPath = path.join(__dirname, '../../logs/app.log');
        
        if (!fs.existsSync(logPath)) {
            return res.status(404).json({ error: '日志文件不存在' });
        }
        
        const logContent = fs.readFileSync(logPath, 'utf8');
        
        res.setHeader('Content-Type', 'text/plain');
        res.setHeader('Content-Disposition', `attachment; filename=sms-logs-${new Date().toISOString().split('T')[0]}.txt`);
        res.send(logContent);
        
    } catch (error) {
        res.status(500).json({ error: '导出日志失败' });
    }
});

// 删除用户
router.delete('/users/:phone', authenticateAdmin, (req, res) => {
    try {
        const { phone } = req.params;
        const usersPath = path.join(__dirname, '../../data/users.json');
        
        if (!fs.existsSync(usersPath)) {
            return res.status(404).json({ error: '用户文件不存在' });
        }
        
        const usersData = fs.readFileSync(usersPath, 'utf8');
        let users = JSON.parse(usersData);
        
        const initialLength = users.length;
        users = users.filter(user => user.phone !== phone);
        
        if (users.length === initialLength) {
            return res.status(404).json({ error: '用户不存在' });
        }
        
        fs.writeFileSync(usersPath, JSON.stringify(users, null, 2));
        
        res.json({ message: '用户删除成功' });
    } catch (error) {
        res.status(500).json({ error: '删除用户失败' });
    }
});

module.exports = router;
