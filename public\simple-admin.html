<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化管理后台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-section {
            text-align: center;
        }
        .admin-section {
            display: none;
        }
        h1 {
            color: #667eea;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .admin-content {
            text-align: center;
        }
        .admin-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录区域 -->
        <div id="loginSection" class="login-section">
            <h1>🔐 管理员登录</h1>
            <div id="message"></div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label>管理员账号</label>
                    <input type="text" id="username" placeholder="请输入管理员账号" required>
                </div>
                <div class="form-group">
                    <label>管理密码</label>
                    <input type="password" id="password" placeholder="请输入管理密码" required>
                </div>
                <button type="submit" class="btn">登录管理后台</button>
            </form>
            
            <div class="debug-info">
                <strong>调试信息：</strong><br>
                默认账号：admin<br>
                默认密码：admin123<br>
                <span id="debugInfo"></span>
            </div>
        </div>

        <!-- 管理区域 -->
        <div id="adminSection" class="admin-section">
            <div class="admin-content">
                <h1>🎛️ 管理后台</h1>
                <div class="admin-info">
                    <p>欢迎，<span id="adminName"></span>！</p>
                    <p>登录时间：<span id="loginTime"></span></p>
                </div>
                
                <button onclick="testAPI()" class="btn">测试API连接</button>
                <button onclick="logout()" class="btn btn-secondary">退出登录</button>
                
                <div id="apiResult" class="admin-info" style="display: none;">
                    <h3>API测试结果</h3>
                    <div id="apiContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示消息
        function showMessage(message, type) {
            const messageEl = document.getElementById('message');
            messageEl.innerHTML = `<div class="message ${type}">${message}</div>`;
            setTimeout(() => {
                messageEl.innerHTML = '';
            }, 3000);
        }

        // 显示调试信息
        function showDebugInfo(info) {
            document.getElementById('debugInfo').innerHTML = '<br>' + info;
        }

        // 登录处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            showDebugInfo(`输入的用户名: "${username}", 密码: "${password}"`);
            
            if (!username || !password) {
                showMessage('请输入管理员账号和密码', 'error');
                return;
            }
            
            // 前端验证
            if (username === 'admin' && password === 'admin123') {
                showMessage('登录成功！', 'success');
                
                // 保存登录状态
                localStorage.setItem('adminToken', 'simple_admin_' + Date.now());
                localStorage.setItem('adminName', username);
                
                // 显示管理界面
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('adminSection').style.display = 'block';
                document.getElementById('adminName').textContent = username;
                document.getElementById('loginTime').textContent = new Date().toLocaleString();
                
            } else {
                showMessage('账号或密码错误！默认账号：admin，密码：admin123', 'error');
                showDebugInfo(`验证失败 - 期望: admin/admin123, 实际: ${username}/${password}`);
            }
        });

        // 退出登录
        function logout() {
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminName');
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('adminSection').style.display = 'none';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            showMessage('已退出登录', 'success');
        }

        // 测试API
        async function testAPI() {
            const resultEl = document.getElementById('apiResult');
            const contentEl = document.getElementById('apiContent');
            
            resultEl.style.display = 'block';
            contentEl.innerHTML = '正在测试API连接...';
            
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    contentEl.innerHTML = `
                        <p>✅ API连接正常</p>
                        <p>状态: ${data.status}</p>
                        <p>运行时间: ${Math.floor(data.uptime)}秒</p>
                    `;
                } else {
                    contentEl.innerHTML = '❌ API响应异常';
                }
            } catch (error) {
                contentEl.innerHTML = `❌ API连接失败: ${error.message}`;
            }
        }

        // 页面加载时检查登录状态
        window.onload = function() {
            const token = localStorage.getItem('adminToken');
            const adminName = localStorage.getItem('adminName');
            
            if (token && adminName) {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('adminSection').style.display = 'block';
                document.getElementById('adminName').textContent = adminName;
                document.getElementById('loginTime').textContent = '之前登录';
            }
        };
    </script>
</body>
</html>
