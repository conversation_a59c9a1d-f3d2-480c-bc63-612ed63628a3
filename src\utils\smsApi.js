// SMS API 封装类
const axios = require('axios');
const config = require('../config/config');

class SMSApi {
  constructor() {
    this.baseURL = config.sms.baseURL;
    this.token = config.sms.token;
    this.timeout = config.sms.timeout;
    
    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${this.token}`
      }
    });

    // 设置请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(`SMS API请求: ${config.method ? config.method.toUpperCase() : 'GET'} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('SMS API请求错误:', error);
        return Promise.reject(error);
      }
    );

    // 设置响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`SMS API响应: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        console.error('SMS API响应错误:', (error.response ? error.response.data : null) || error.message);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  // 错误处理
  handleError(error) {
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      switch (status) {
        case 401:
          return new Error('API认证失败，请检查Token');
        case 403:
          return new Error('API权限不足');
        case 429:
          return new Error('API请求频率过高，请稍后重试');
        case 500:
          return new Error('SMS服务器内部错误');
        default:
          return new Error((data ? data.message : null) || `API请求失败 (${status})`);
      }
    } else if (error.request) {
      // 网络错误
      return new Error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      return new Error(error.message || '未知错误');
    }
  }

  // 发送短信
  async sendSMS(recipients, message, senderId = 'YourName', type = 'plain') {
    try {
      console.log('=== GoTone SMS API 调用开始 ===');
      console.log('Base URL:', this.baseURL);
      console.log('Token:', this.token ? this.token.substring(0, 10) + '...' : 'null');

      // 处理接收者格式
      const recipientList = Array.isArray(recipients)
        ? recipients.join(',')
        : recipients.toString();

      const requestData = {
        recipient: recipientList,
        sender_id: senderId,
        type: type,
        message: message
      };

      console.log('请求数据:', requestData);
      console.log('完整URL:', `${this.baseURL}/sms/send`);

      const response = await this.client.post('/sms/send', requestData);

      console.log('GoTone API 响应:', response.data);

      return {
        success: true,
        data: response.data,
        message: '短信发送成功'
      };
    } catch (error) {
      console.error('=== GoTone SMS API 调用失败 ===');
      console.error('错误类型:', error.constructor.name);
      console.error('错误消息:', error.message);

      if (error.response) {
        console.error('响应状态:', error.response.status);
        console.error('响应数据:', error.response.data);
        console.error('响应头:', error.response.headers);
      } else if (error.request) {
        console.error('请求配置:', error.config);
        console.error('没有收到响应');
      }

      throw error;
    }
  }

  // 批量发送短信
  async sendBatchSMS(recipients, message, senderId = 'YourName') {
    if (!Array.isArray(recipients) || recipients.length === 0) {
      throw new Error('接收者列表不能为空');
    }

    if (recipients.length > 1000) {
      throw new Error('单次批量发送不能超过1000个号码');
    }

    return await this.sendSMS(recipients, message, senderId);
  }

  // 发送营销短信
  async sendCampaign(campaignData) {
    try {
      const response = await this.client.post('/sms/campaign', campaignData);
      
      return {
        success: true,
        data: response.data,
        message: '营销短信发送成功'
      };
    } catch (error) {
      console.error('营销短信发送失败:', error);
      throw error;
    }
  }

  // 获取短信发送记录
  async getSMSHistory(params = {}) {
    try {
      const response = await this.client.get('/sms', { params });
      
      return {
        success: true,
        data: response.data,
        message: '获取短信记录成功'
      };
    } catch (error) {
      console.error('获取短信记录失败:', error);
      throw error;
    }
  }

  // 获取账户余额
  async getBalance() {
    try {
      const response = await this.client.get('/account/balance');
      
      return {
        success: true,
        data: response.data,
        message: '获取账户余额成功'
      };
    } catch (error) {
      console.error('获取账户余额失败:', error);
      throw error;
    }
  }

  // 验证API连接
  async testConnection() {
    try {
      const response = await this.client.get('/account/profile');
      
      return {
        success: true,
        data: response.data,
        message: 'API连接正常'
      };
    } catch (error) {
      console.error('API连接测试失败:', error);
      throw error;
    }
  }

  // 解析号码文件
  parsePhoneNumbers(fileContent) {
    try {
      // 支持多种分隔符：换行、逗号、分号
      const phones = fileContent
        .split(/[\n\r,;]/)
        .map(phone => phone.trim())
        .filter(phone => phone && /^\d+$/.test(phone))
        .filter((phone, index, arr) => arr.indexOf(phone) === index); // 去重

      return {
        success: true,
        phones: phones,
        count: phones.length,
        message: `成功解析 ${phones.length} 个号码`
      };
    } catch (error) {
      console.error('号码解析失败:', error);
      throw new Error('号码文件格式错误');
    }
  }

  // 验证手机号格式
  validatePhoneNumber(phone) {
    // 简单的手机号验证（可根据需要调整）
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  // 批量验证手机号
  validatePhoneNumbers(phones) {
    const validPhones = [];
    const invalidPhones = [];

    phones.forEach(phone => {
      if (this.validatePhoneNumber(phone)) {
        validPhones.push(phone);
      } else {
        invalidPhones.push(phone);
      }
    });

    return {
      valid: validPhones,
      invalid: invalidPhones,
      validCount: validPhones.length,
      invalidCount: invalidPhones.length
    };
  }
}

module.exports = new SMSApi();
