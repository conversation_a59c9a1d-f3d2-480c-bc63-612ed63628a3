<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            cursor: pointer;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎛️ 管理后台测试页面</h1>
        <p>如果您能看到这个页面，说明服务器运行正常</p>
        
        <div class="status" id="status">
            <p>正在检查服务器状态...</p>
        </div>
        
        <div>
            <a href="/" class="btn">返回客户页面</a>
            <a href="/admin.html" class="btn">访问完整管理后台</a>
            <button onclick="testAPI()" class="btn">测试API连接</button>
        </div>
    </div>

    <script>
        // 测试API连接
        async function testAPI() {
            const statusEl = document.getElementById('status');
            statusEl.innerHTML = '<p>正在测试API连接...</p>';
            
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    statusEl.innerHTML = `
                        <p>✅ API连接正常</p>
                        <p>服务器状态: ${data.status}</p>
                        <p>运行时间: ${Math.floor(data.uptime)}秒</p>
                    `;
                } else {
                    statusEl.innerHTML = '<p>❌ API响应异常</p>';
                }
            } catch (error) {
                statusEl.innerHTML = `<p>❌ API连接失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
