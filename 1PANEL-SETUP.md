# 🔧 1Panel + OpenResty 环境配置指南

## 📁 您的文件路径
```
/opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system
```

## 🚀 快速配置步骤

### 1. 确认文件位置
确保您的文件已放在正确路径：
```bash
cd /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system
ls -la
```

应该看到：
```
├── server.js
├── package.json
├── public/
├── src/
├── config/
├── scripts/
└── ...
```

### 2. 安装依赖
```bash
cd /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system
npm install
```

### 3. 配置环境变量
```bash
cp config/.env.example .env
nano .env
```

编辑 `.env` 文件：
```env
PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-in-production
GOTONE_TOKEN=YOUR_GOTONE_API_TOKEN_HERE
NODE_ENV=production
```

### 4. 在1Panel中配置网站

#### 方法一：通过1Panel面板配置（推荐）

1. **登录1Panel管理面板**
2. **进入网站管理**
3. **找到 `dailuanshej.cn` 网站**
4. **编辑网站配置**
5. **添加子域名配置**：

在网站配置中添加：
```nginx
# SMS系统子域名配置
server {
    listen 80;
    server_name api.dailuanshej.cn;
    
    # 静态文件根目录
    root /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system/public;
    index index.html;
    
    # 静态文件直接服务
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # API请求代理到Node.js
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 主页面和其他请求
    location / {
        try_files $uri $uri/ @nodejs;
    }
    
    # 回退到Node.js处理
    location @nodejs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 方法二：手动配置Nginx

如果1Panel不支持直接编辑，可以手动添加配置文件：

```bash
# 复制配置文件
sudo cp config/nginx.conf /opt/1panel/apps/openresty/openresty/conf/conf.d/api-dailuanshej.conf

# 测试配置
sudo /opt/1panel/apps/openresty/openresty/sbin/nginx -t

# 重载配置
sudo /opt/1panel/apps/openresty/openresty/sbin/nginx -s reload
```

### 5. 启动Node.js应用

#### 使用PM2（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
cd /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system
pm2 start config/ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

#### 直接启动
```bash
cd /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system
nohup node server.js > logs/app.log 2>&1 &
```

### 6. 验证配置

#### 检查Node.js应用
```bash
# 检查进程
ps aux | grep node

# 检查端口
netstat -tlnp | grep :3000

# 检查日志
tail -f logs/app.log
```

#### 检查Nginx配置
```bash
# 测试配置
sudo /opt/1panel/apps/openresty/openresty/sbin/nginx -t

# 查看错误日志
tail -f /opt/1panel/apps/openresty/openresty/logs/error.log
```

#### 测试访问
1. **本地测试**: `curl http://localhost:3000`
2. **域名测试**: `curl http://api.dailuanshej.cn`
3. **浏览器访问**: https://api.dailuanshej.cn

## 🔧 故障排除

### 问题1：页面显示404或指向错误
**原因**: Nginx配置路径不正确
**解决**: 
1. 检查1Panel中的网站配置
2. 确认root路径指向正确的public目录
3. 重载Nginx配置

### 问题2：API请求失败
**原因**: Node.js应用未启动或端口不对
**解决**:
```bash
# 检查Node.js进程
ps aux | grep node

# 检查端口占用
netstat -tlnp | grep :3000

# 重启应用
pm2 restart sms-system
```

### 问题3：静态文件加载失败
**原因**: 静态文件路径配置错误
**解决**:
1. 确认public目录下有css、js、assets文件夹
2. 检查Nginx静态文件配置
3. 查看浏览器开发者工具网络面板

### 问题4：1Panel面板无法编辑配置
**解决**:
1. 直接编辑配置文件：`/opt/1panel/apps/openresty/openresty/conf/nginx.conf`
2. 或在conf.d目录添加单独配置文件
3. 重载配置：`sudo /opt/1panel/apps/openresty/openresty/sbin/nginx -s reload`

## 📱 测试功能

配置完成后，访问 https://api.dailuanshej.cn 应该能看到：
1. ✅ 短信系统登录页面
2. ✅ 用户注册功能
3. ✅ 短信发送功能
4. ✅ 文件上传功能

## 🔍 日志查看

```bash
# Node.js应用日志
tail -f /opt/1panel/apps/openresty/openresty/www/sites/dailuanshej.cn/index/sms-system/logs/app.log

# Nginx访问日志
tail -f /opt/1panel/apps/openresty/openresty/logs/access.log

# Nginx错误日志
tail -f /opt/1panel/apps/openresty/openresty/logs/error.log

# PM2日志
pm2 logs sms-system
```

## 💡 优化建议

1. **SSL证书**: 在1Panel中为api.dailuanshej.cn配置SSL证书
2. **防火墙**: 确保3000端口在内网开放，80/443端口对外开放
3. **监控**: 使用PM2监控Node.js应用状态
4. **备份**: 定期备份data/users.json文件
5. **日志轮转**: 配置日志轮转避免日志文件过大
