# 轻量级短信发送系统

基于 Node.js + Express 开发的轻量级短信发送系统，支持用户注册登录、单发短信、批量发送、号码导入等功能。

## 🎯 功能特性

- ✅ **用户认证** - 手机号+密码注册登录
- ✅ **单发短信** - 发送给单个号码
- ✅ **批量发送** - 同时发送给多个号码
- ✅ **号码导入** - 支持CSV/TXT文件批量导入
- ✅ **响应式设计** - 适配手机和电脑
- ✅ **轻量级部署** - 文件极少，部署简单

## 📁 项目结构（有组织的文件夹）

```
sms-system/
├── server.js              # 主服务器文件
├── package.json           # 项目配置
├── public/                # 前端静态文件
│   ├── index.html         # 主页面
│   ├── css/               # 样式文件夹
│   │   └── style.css      # 主样式文件
│   ├── js/                # JavaScript文件夹
│   │   └── script.js      # 前端脚本
│   └── assets/            # 静态资源
│       └── favicon.ico    # 网站图标
├── src/                   # 源代码文件夹
│   ├── routes/            # 路由文件夹
│   │   ├── auth.js        # 认证路由
│   │   └── sms.js         # 短信路由
│   ├── middleware/        # 中间件文件夹
│   │   └── auth.js        # 认证中间件
│   ├── utils/             # 工具函数文件夹
│   │   ├── database.js    # 数据库操作
│   │   └── smsApi.js      # SMS API封装
│   └── config/            # 配置文件夹
│       └── config.js      # 应用配置
├── data/                  # 数据存储文件夹
│   └── users.json         # 用户数据
├── uploads/               # 文件上传目录
├── logs/                  # 日志目录
├── config/                # 部署配置文件夹
│   ├── .env.example       # 环境配置示例
│   ├── ecosystem.config.js # PM2配置
│   └── nginx.conf         # Nginx配置
├── scripts/               # 脚本文件夹
│   └── deploy.sh          # 部署脚本
└── README.md              # 说明文档
```

## 🚀 快速开始

### 1. 环境要求

- Node.js 16+ 
- npm 8+
- GoTone SMS API Token

### 2. 安装部署

```bash
# 克隆或下载项目文件到服务器
cd /var/www/sms-system

# 运行部署脚本
chmod +x deploy.sh
./deploy.sh

# 或手动安装
npm install
cp .env.example .env
# 编辑 .env 文件配置API Token
node server.js
```

### 3. 配置环境变量

编辑 `.env` 文件：

```env
PORT=3000
JWT_SECRET=your-super-secret-jwt-key-change-in-production
GOTONE_TOKEN=YOUR_GOTONE_API_TOKEN_HERE
NODE_ENV=production
```

### 4. 启动应用

```bash
# 开发模式
npm run dev

# 生产模式
npm start

# 使用PM2（推荐）
pm2 start config/ecosystem.config.js --env production

# 或者使用部署脚本（推荐）
./scripts/deploy.sh
```

## 🌐 域名配置

### 方案一：直接运行在3000端口

访问：`http://api.dailuanshej.cn:3000`

### 方案二：使用Nginx反向代理（推荐）

1. **安装Nginx**
   ```bash
   # Ubuntu/Debian
   sudo apt install nginx
   
   # CentOS/RHEL
   sudo yum install nginx
   ```

2. **配置Nginx**
   ```bash
   # 复制配置文件
   sudo cp config/nginx.conf /etc/nginx/sites-available/sms-system
   sudo ln -s /etc/nginx/sites-available/sms-system /etc/nginx/sites-enabled/
   
   # 测试配置
   sudo nginx -t
   
   # 重启Nginx
   sudo systemctl restart nginx
   ```

3. **域名解析**
   - 将 `api.dailuanshej.cn` 解析到服务器IP（✅ 已完成）
   - 访问：`https://api.dailuanshej.cn`

### 方案三：放在根目录

如果要将文件直接放在服务器根目录：

```bash
# 将项目文件复制到网站根目录
cp -r sms-system/* /var/www/html/

# 在根目录启动
cd /var/www/html
npm install
node server.js
```

## 📱 使用说明

### 1. 用户注册
- 打开网站首页
- 点击"注册"选项卡
- 输入手机号、用户名（可选）、密码
- 点击注册

### 2. 用户登录
- 输入注册时的手机号和密码
- 点击登录

### 3. 发送短信

**单发短信：**
- 选择"单发短信"选项卡
- 输入接收号码和短信内容
- 点击发送

**批量发送：**
- 选择"批量发送"选项卡
- 输入多个号码（每行一个或逗号分隔）
- 输入短信内容
- 点击批量发送

**导入号码：**
- 选择"导入号码"选项卡
- 上传包含号码的TXT或CSV文件
- 输入短信内容
- 点击发送给所有导入的号码

## 🔧 API接口

### 用户认证

```bash
# 注册
POST /api/register
{
  "phone": "13800138000",
  "username": "用户名",
  "password": "密码"
}

# 登录
POST /api/login
{
  "phone": "13800138000",
  "password": "密码"
}
```

### 短信发送

```bash
# 发送短信
POST /api/send-sms
Authorization: Bearer <token>
{
  "recipients": ["13800138000", "13800138001"],
  "message": "短信内容",
  "sender_id": "YourName"
}
```

### 文件上传

```bash
# 导入联系人
POST /api/import-contacts
Authorization: Bearer <token>
Content-Type: multipart/form-data
file: <文件>
```

## 🛠️ 运维管理

### 查看日志

```bash
# 应用日志
tail -f logs/app.log

# PM2日志
pm2 logs sms-system

# Nginx日志
tail -f /var/log/nginx/sms-system-access.log
```

### 进程管理

```bash
# PM2管理
pm2 list                    # 查看进程
pm2 restart sms-system      # 重启应用
pm2 stop sms-system         # 停止应用
pm2 delete sms-system       # 删除应用

# 开机自启
pm2 startup
pm2 save
```

### 备份数据

```bash
# 备份用户数据
cp data/users.json data/users.json.backup

# 定期备份（添加到crontab）
0 2 * * * cp /var/www/sms-system/data/users.json /backup/users-$(date +\%Y\%m\%d).json
```

## 🔒 安全建议

1. **更改默认密钥**
   - 修改 `.env` 中的 `JWT_SECRET`
   - 使用强密码

2. **启用HTTPS**
   - 申请SSL证书
   - 配置Nginx HTTPS

3. **防火墙设置**
   ```bash
   # 只开放必要端口
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw enable
   ```

4. **定期更新**
   ```bash
   npm audit
   npm update
   ```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :3000
   
   # 修改端口
   # 编辑 .env 文件中的 PORT
   ```

2. **权限错误**
   ```bash
   # 设置正确权限
   chmod 755 data uploads logs
   chown -R www-data:www-data /var/www/sms-system
   ```

3. **API调用失败**
   - 检查 GoTone API Token 是否正确
   - 确认网络连接正常
   - 查看应用日志

4. **文件上传失败**
   - 检查 uploads 目录权限
   - 确认文件格式正确

## 📞 技术支持

如遇问题，请检查：
1. 服务器日志：`logs/app.log`
2. Nginx日志：`/var/log/nginx/`
3. 浏览器控制台错误
4. 网络连接状态

## 📄 许可证

MIT License
