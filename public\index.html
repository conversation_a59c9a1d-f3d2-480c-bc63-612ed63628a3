<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信发送系统 - api.dailuanshej.cn</title>
    <meta name="description" content="轻量级短信发送系统，支持单发、批量发送、号码导入">
    <meta name="keywords" content="短信发送,SMS,批量短信,GoTone API">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>📱 短信发送系统</h1>
            <p class="subtitle">api.dailuanshej.cn - 轻量级短信发送平台</p>
            <div class="user-info" id="userInfo" style="display: none;">
                <span id="username"></span>
                <a href="/admin.html" class="btn btn-info" style="margin-right: 10px;">管理后台</a>
                <button onclick="logout()" class="btn btn-secondary">退出</button>
            </div>
        </header>

        <!-- 登录/注册表单 -->
        <div id="authSection" class="auth-section">
            <div class="auth-tabs">
                <button class="tab-btn active" onclick="showLogin()">登录</button>
                <button class="tab-btn" onclick="showRegister()">注册</button>
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" class="auth-form">
                <h2>用户登录</h2>
                <div class="form-group">
                    <label>手机号</label>
                    <input type="tel" id="loginPhone" placeholder="请输入手机号" required>
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="loginPassword" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="btn btn-primary">登录</button>
            </form>

            <!-- 注册表单 -->
            <form id="registerForm" class="auth-form" style="display: none;">
                <h2>用户注册</h2>
                <div class="form-group">
                    <label>手机号</label>
                    <input type="tel" id="registerPhone" placeholder="请输入手机号" required>
                </div>
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="registerUsername" placeholder="请输入用户名（可选）">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="registerPassword" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="btn btn-primary">注册</button>
            </form>
        </div>

        <!-- 主功能区 -->
        <div id="mainSection" class="main-section" style="display: none;">
            <!-- 功能选项卡 -->
            <div class="function-tabs">
                <button class="tab-btn active" onclick="showSingleSMS()">单发短信</button>
                <button class="tab-btn" onclick="showBatchSMS()">批量发送</button>
                <button class="tab-btn" onclick="showImportContacts()">导入号码</button>
            </div>

            <!-- 单发短信 -->
            <div id="singleSMSSection" class="function-section">
                <h3>📱 单发短信</h3>
                <form id="singleSMSForm">
                    <div class="form-group">
                        <label>接收号码</label>
                        <input type="tel" id="singleRecipient" placeholder="请输入手机号" required>
                    </div>
                    <div class="form-group">
                        <label>发送方ID</label>
                        <input type="text" id="singleSenderId" value="YourName" placeholder="发送方标识">
                    </div>
                    <div class="form-group">
                        <label>短信内容</label>
                        <textarea id="singleMessage" placeholder="请输入短信内容" rows="4" required></textarea>
                        <div class="char-count">
                            <span id="singleCharCount">0</span>/500
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">发送短信</button>
                </form>
            </div>

            <!-- 批量发送 -->
            <div id="batchSMSSection" class="function-section" style="display: none;">
                <h3>📢 批量发送</h3>
                <form id="batchSMSForm">
                    <div class="form-group">
                        <label>接收号码</label>
                        <textarea id="batchRecipients" placeholder="请输入多个手机号，每行一个或用逗号分隔" rows="5" required></textarea>
                        <div class="form-tip">支持每行一个号码或用逗号分隔</div>
                    </div>
                    <div class="form-group">
                        <label>发送方ID</label>
                        <input type="text" id="batchSenderId" value="YourName" placeholder="发送方标识">
                    </div>
                    <div class="form-group">
                        <label>短信内容</label>
                        <textarea id="batchMessage" placeholder="请输入短信内容" rows="4" required></textarea>
                        <div class="char-count">
                            <span id="batchCharCount">0</span>/500
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">批量发送</button>
                </form>
            </div>

            <!-- 导入号码 -->
            <div id="importContactsSection" class="function-section" style="display: none;">
                <h3>📋 导入号码</h3>
                <div class="import-area">
                    <div class="form-group">
                        <label>选择文件</label>
                        <input type="file" id="contactFile" accept=".txt,.csv" required>
                        <div class="form-tip">支持 .txt 和 .csv 文件，每行一个号码</div>
                    </div>
                    <button onclick="importContacts()" class="btn btn-secondary">导入号码</button>
                </div>
                
                <div id="importedContacts" class="imported-contacts" style="display: none;">
                    <h4>已导入的号码</h4>
                    <div class="contact-list" id="contactList"></div>
                    <div class="batch-actions">
                        <div class="form-group">
                            <label>发送方ID</label>
                            <input type="text" id="importSenderId" value="YourName" placeholder="发送方标识">
                        </div>
                        <div class="form-group">
                            <label>短信内容</label>
                            <textarea id="importMessage" placeholder="请输入短信内容" rows="4" required></textarea>
                            <div class="char-count">
                                <span id="importCharCount">0</span>/500
                            </div>
                        </div>
                        <button onclick="sendToImportedContacts()" class="btn btn-primary">发送给所有导入的号码</button>
                    </div>
                </div>
            </div>


        </div>

        <!-- 结果显示 -->
        <div id="resultSection" class="result-section" style="display: none;">
            <h3>发送结果</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>

    <!-- 消息提示 -->
    <div id="message" class="message" style="display: none;"></div>

    <script src="js/script.js"></script>
</body>
</html>
