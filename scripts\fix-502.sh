#!/bin/bash

echo "🔧 修复502错误 - 快速诊断和修复脚本"
echo "========================================"

# 检查当前路径
CURRENT_PATH=$(pwd)
echo "📁 当前路径: $CURRENT_PATH"

if [[ $CURRENT_PATH != *"sms-system"* ]]; then
    echo "❌ 请在sms-system目录中运行此脚本"
    exit 1
fi

# 1. 检查Node.js应用状态
echo ""
echo "🔍 检查Node.js应用状态..."
NODE_PROCESS=$(ps aux | grep "node.*server.js" | grep -v grep)
if [ -z "$NODE_PROCESS" ]; then
    echo "❌ Node.js应用未运行"
    NEED_START=true
else
    echo "✅ Node.js应用正在运行"
    echo "   进程: $NODE_PROCESS"
    NEED_START=false
fi

# 2. 检查端口3000
echo ""
echo "🔌 检查端口3000..."
PORT_CHECK=$(netstat -tlnp 2>/dev/null | grep :3000)
if [ -z "$PORT_CHECK" ]; then
    echo "❌ 端口3000未监听"
    NEED_START=true
else
    echo "✅ 端口3000正在监听"
    echo "   $PORT_CHECK"
fi

# 3. 检查依赖
echo ""
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  node_modules不存在，安装依赖..."
    npm install
else
    echo "✅ 依赖已安装"
fi

# 4. 检查配置文件
echo ""
echo "⚙️ 检查配置文件..."
if [ ! -f ".env" ]; then
    echo "⚠️  .env文件不存在，创建配置文件..."
    cp config/.env.example .env
    echo "✅ .env文件已创建"
    echo "⚠️  请编辑.env文件配置GOTONE_TOKEN"
else
    echo "✅ .env文件存在"
fi

# 5. 创建必要目录
echo ""
echo "📁 创建必要目录..."
mkdir -p data logs uploads
echo "✅ 目录创建完成"

# 6. 启动应用（如果需要）
if [ "$NEED_START" = true ]; then
    echo ""
    echo "🚀 启动Node.js应用..."
    
    # 停止可能存在的进程
    pkill -f "node.*server.js" 2>/dev/null
    
    # 启动新进程
    nohup node server.js > logs/app.log 2>&1 &
    
    echo "✅ 应用启动中..."
    sleep 3
    
    # 检查启动结果
    if ps aux | grep "node.*server.js" | grep -v grep > /dev/null; then
        echo "✅ Node.js应用启动成功"
    else
        echo "❌ Node.js应用启动失败"
        echo "   查看日志: tail -f logs/app.log"
        exit 1
    fi
fi

# 7. 测试本地连接
echo ""
echo "🧪 测试本地连接..."
sleep 2

# 测试健康检查
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    echo "   查看日志: tail -f logs/app.log"
fi

# 测试主页
if curl -s http://localhost:3000 | grep -q "短信"; then
    echo "✅ 主页响应正常"
else
    echo "⚠️  主页响应异常"
fi

# 8. 检查Nginx配置
echo ""
echo "🌐 检查Nginx配置..."
if nginx -t 2>/dev/null; then
    echo "✅ Nginx配置语法正确"
else
    echo "❌ Nginx配置有错误"
    echo "   运行: nginx -t 查看详细错误"
fi

# 9. 显示日志
echo ""
echo "📋 最近的应用日志:"
echo "===================="
if [ -f "logs/app.log" ]; then
    tail -10 logs/app.log
else
    echo "日志文件不存在"
fi

# 10. 显示诊断结果
echo ""
echo "🎯 诊断结果和建议:"
echo "=================="

if ps aux | grep "node.*server.js" | grep -v grep > /dev/null; then
    if netstat -tlnp 2>/dev/null | grep :3000 > /dev/null; then
        echo "✅ Node.js应用运行正常"
        echo ""
        echo "🔧 如果仍然502错误，请检查:"
        echo "1. Cloudflare DNS设置 - 确保A记录指向正确IP"
        echo "2. Cloudflare SSL设置 - 使用Full或Full(strict)"
        echo "3. 服务器防火墙 - 确保80/443端口开放"
        echo "4. Nginx配置 - 确保proxy_pass指向localhost:3000"
        echo ""
        echo "🧪 测试命令:"
        echo "   curl http://localhost:3000"
        echo "   curl https://api.dailuanshej.cn"
    else
        echo "❌ 端口3000未监听，应用可能启动失败"
    fi
else
    echo "❌ Node.js应用未运行"
    echo "   手动启动: nohup node server.js > logs/app.log 2>&1 &"
fi

echo ""
echo "📞 查看实时日志: tail -f logs/app.log"
echo "🔍 检查Nginx日志: tail -f /www/sites/dailuanshej.cn/log/api-error.log"
